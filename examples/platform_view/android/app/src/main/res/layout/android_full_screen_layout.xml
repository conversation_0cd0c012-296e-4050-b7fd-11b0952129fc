<!-- Copyright 2014 The Flutter Authors. All rights reserved.
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file. -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:theme="@style/Theme.AppCompat">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/my_toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:elevation="4dp"
        android:theme="@style/ThemeOverlay.AppCompat.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light"/>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/grey">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:layout_weight="1"
                android:gravity="center">


                <TextView
                    android:id="@+id/button_tap"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/button_tap"
                    android:padding="18dp"
                    android:textSize="@dimen/font_size" />

                <Button
                    android:id="@+id/button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:theme="@style/MyButton"
                    android:text="@string/switch_view"
                    android:textAllCaps="false" />
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/edge_margin"
                android:background="@color/grey"
                android:text="@string/android"
                android:textSize="@dimen/platform_label_font_size" />

        </LinearLayout>

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fab"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|right"
            android:layout_margin="@dimen/edge_margin"
            android:clickable="true"
            android:elevation="@dimen/fab_elevation_resting"
            android:src="@drawable/ic_add_black_24dp"
            app:backgroundTint="@color/white"
            app:fabSize="normal"
            app:pressedTranslationZ="@dimen/fab_elevation_pressed"
            app:rippleColor="@color/grey" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>


</LinearLayout>
