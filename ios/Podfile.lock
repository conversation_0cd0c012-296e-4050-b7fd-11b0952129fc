PODS:
  - app_settings (5.1.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_keyboard_visibility_temp_fork (0.0.1):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - get_thumbnail_video (0.0.1):
    - Flutter
    - libwebp
  - icloud_availability (0.0.1):
    - Flutter
  - icloud_storage (0.0.1):
    - Flutter
  - image_editor_common (1.0.0):
    - Flutter
  - image_gallery_saver (2.0.2):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - in_app_purchase_storekit (0.0.1):
    - Flutter
    - FlutterMacOS
  - in_app_review (0.2.0):
    - Flutter
  - libwebp (1.3.2):
    - libwebp/demux (= 1.3.2)
    - libwebp/mux (= 1.3.2)
    - libwebp/sharpyuv (= 1.3.2)
    - libwebp/webp (= 1.3.2)
  - libwebp/demux (1.3.2):
    - libwebp/webp
  - libwebp/mux (1.3.2):
    - libwebp/demux
  - libwebp/sharpyuv (1.3.2)
  - libwebp/webp (1.3.2):
    - libwebp/sharpyuv
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - photo_manager (2.0.0):
    - Flutter
    - FlutterMacOS
  - PostHog (3.8.1)
  - posthog_flutter (0.0.1):
    - Flutter
    - FlutterMacOS
    - PostHog (~> 3.0)
  - quill_native_bridge_ios (0.0.1):
    - Flutter
  - r_string_transform (0.0.1):
    - Flutter
  - SDWebImage (5.19.7):
    - SDWebImage/Core (= 5.19.7)
  - SDWebImage/Core (5.19.7)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - Sentry/HybridSDK (8.35.1)
  - sentry_flutter (8.8.0):
    - Flutter
    - FlutterMacOS
    - Sentry/HybridSDK (= 8.35.1)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - Toast (4.1.1)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - app_settings (from `.symlinks/plugins/app_settings/ios`)
  - Flutter (from `Flutter`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_keyboard_visibility_temp_fork (from `.symlinks/plugins/flutter_keyboard_visibility_temp_fork/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - get_thumbnail_video (from `.symlinks/plugins/get_thumbnail_video/ios`)
  - icloud_availability (from `.symlinks/plugins/icloud_availability/ios`)
  - icloud_storage (from `.symlinks/plugins/icloud_storage/ios`)
  - image_editor_common (from `.symlinks/plugins/image_editor_common/ios`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - in_app_purchase_storekit (from `.symlinks/plugins/in_app_purchase_storekit/darwin`)
  - in_app_review (from `.symlinks/plugins/in_app_review/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - posthog_flutter (from `.symlinks/plugins/posthog_flutter/ios`)
  - quill_native_bridge_ios (from `.symlinks/plugins/quill_native_bridge_ios/ios`)
  - r_string_transform (from `.symlinks/plugins/r_string_transform/ios`)
  - sentry_flutter (from `.symlinks/plugins/sentry_flutter/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - libwebp
    - Mantle
    - PostHog
    - SDWebImage
    - SDWebImageWebPCoder
    - Sentry
    - Toast

EXTERNAL SOURCES:
  app_settings:
    :path: ".symlinks/plugins/app_settings/ios"
  Flutter:
    :path: Flutter
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_keyboard_visibility_temp_fork:
    :path: ".symlinks/plugins/flutter_keyboard_visibility_temp_fork/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  get_thumbnail_video:
    :path: ".symlinks/plugins/get_thumbnail_video/ios"
  icloud_availability:
    :path: ".symlinks/plugins/icloud_availability/ios"
  icloud_storage:
    :path: ".symlinks/plugins/icloud_storage/ios"
  image_editor_common:
    :path: ".symlinks/plugins/image_editor_common/ios"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  in_app_purchase_storekit:
    :path: ".symlinks/plugins/in_app_purchase_storekit/darwin"
  in_app_review:
    :path: ".symlinks/plugins/in_app_review/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  posthog_flutter:
    :path: ".symlinks/plugins/posthog_flutter/ios"
  quill_native_bridge_ios:
    :path: ".symlinks/plugins/quill_native_bridge_ios/ios"
  r_string_transform:
    :path: ".symlinks/plugins/r_string_transform/ios"
  sentry_flutter:
    :path: ".symlinks/plugins/sentry_flutter/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  app_settings: 017320c6a680cdc94c799949d95b84cb69389ebc
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_image_compress_common: ec1d45c362c9d30a3f6a0426c297f47c52007e3e
  flutter_keyboard_visibility_temp_fork: 442dadca3b81868a225cd6a2f605bffff1215844
  flutter_local_notifications: 4cde75091f6327eb8517fa068a0a5950212d2086
  flutter_secure_storage: d33dac7ae2ea08509be337e775f6b59f1ff45f12
  fluttertoast: e9a18c7be5413da53898f660530c56f35edfba9c
  get_thumbnail_video: b9a180957daed3e9179e66268db51d8798e41f65
  icloud_availability: e8d5df18a24008ee1a9979a4ad48573b2b71b1b6
  icloud_storage: d9ac7a33ced81df08ba7ea1bf3099cc0ee58f60a
  image_editor_common: d6f6644ae4a6de80481e89fe6d0a8c49e30b4b43
  image_gallery_saver: cb43cc43141711190510e92c460eb1655cd343cb
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  in_app_purchase_storekit: 8c3b0b3eb1b0f04efbff401c3de6266d4258d433
  in_app_review: 318597b3a06c22bb46dc454d56828c85f444f99d
  libwebp: 1786c9f4ff8a279e4dac1e8f385004d5fc253009
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  package_info_plus: 58f0028419748fad15bf008b270aaa8e54380b1c
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  photo_manager: ff695c7a1dd5bc379974953a2b5c0a293f7c4c8a
  PostHog: fb8855a16d262616e63c1195550d018ab9e97baa
  posthog_flutter: e80defebf021b13375b351c838c9b87d769d7546
  quill_native_bridge_ios: 277bdf5bf0fa5d7a12999556b415a5c63dd76ec4
  r_string_transform: 4d9d2aad1d9b0104093c3bcd54b170a263b3bc69
  SDWebImage: 8a6b7b160b4d710e2a22b6900e25301075c34cb3
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  Sentry: 1fe34e9c2cbba1e347623610d26db121dcb569f1
  sentry_flutter: a39c2a2d67d5e5b9cb0b94a4985c76dd5b3fc737
  share_plus: 8875f4f2500512ea181eef553c3e27dba5135aad
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite: 673a0e54cc04b7d6dba8d24fb8095b31c3a99eec
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  video_player_avfoundation: 7c6c11d8470e1675df7397027218274b6d2360b3
  webview_flutter_wkwebview: 0982481e3d9c78fd5c6f62a002fcd24fc791f1e4

PODFILE CHECKSUM: 075ddf6b19cdcced44581bd8fbdfb58404a78f8a

COCOAPODS: 1.15.2
