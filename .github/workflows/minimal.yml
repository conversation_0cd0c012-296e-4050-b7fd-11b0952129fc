# Copyright 2023 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

name: Minimal workflow to test github action token

on:
  workflow_dispatch

permissions: write-all

jobs:
  minimal_token_test:
    name: minimal_token_test
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    steps:
      - name: Checkout Flutter Repo
        uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332
        with:
          repository: flutter/flutter
          token: ${{ github.token }}
          path: flutter
          ref: master
          fetch-depth: 0
      - name: Write a random file
        run: |
          cd flutter
          touch empty.json
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@c5a7806660adbe173f04e3e038b0ccdcd758773c
        with:
          path: flutter
          commit-message: blah
          committer: GitHub <<EMAIL>>
          token: ${{ secrets.FLUTTERACTIONSBOT_CP_TOKEN }}
          labels: |
            cp: review
          title: '[github actions] testing purposes'
          reviewers: x<PERSON><PERSON><PERSON>
