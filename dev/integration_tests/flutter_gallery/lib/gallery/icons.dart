// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'package:flutter/material.dart';

abstract final class GalleryIcons {
  static const IconData tooltip = IconData(0xe900, fontFamily: 'GalleryIcons');
  static const IconData text_fields_alt = IconData(0xe901, fontFamily: 'GalleryIcons');
  static const IconData tabs = IconData(0xe902, fontFamily: 'GalleryIcons');
  static const IconData switches = IconData(0xe903, fontFamily: 'GalleryIcons');
  static const IconData sliders = IconData(0xe904, fontFamily: 'GalleryIcons');
  static const IconData shrine = IconData(0xe905, fontFamily: 'GalleryIcons');
  static const IconData sentiment_very_satisfied = IconData(0xe906, fontFamily: 'GalleryIcons');
  static const IconData refresh = IconData(0xe907, fontFamily: 'GalleryIcons');
  static const IconData progress_activity = IconData(0xe908, fontFamily: 'GalleryIcons');
  static const IconData phone_iphone = IconData(0xe909, fontFamily: 'GalleryIcons');
  static const IconData page_control = IconData(0xe90a, fontFamily: 'GalleryIcons');
  static const IconData more_vert = IconData(0xe90b, fontFamily: 'GalleryIcons');
  static const IconData menu = IconData(0xe90c, fontFamily: 'GalleryIcons');
  static const IconData list_alt = IconData(0xe90d, fontFamily: 'GalleryIcons');
  static const IconData grid_on = IconData(0xe90e, fontFamily: 'GalleryIcons');
  static const IconData expand_all = IconData(0xe90f, fontFamily: 'GalleryIcons');
  static const IconData event = IconData(0xe910, fontFamily: 'GalleryIcons');
  static const IconData drive_video = IconData(0xe911, fontFamily: 'GalleryIcons');
  static const IconData dialogs = IconData(0xe912, fontFamily: 'GalleryIcons');
  static const IconData data_table = IconData(0xe913, fontFamily: 'GalleryIcons');
  static const IconData custom_typography = IconData(0xe914, fontFamily: 'GalleryIcons');
  static const IconData colors = IconData(0xe915, fontFamily: 'GalleryIcons');
  static const IconData chips = IconData(0xe916, fontFamily: 'GalleryIcons');
  static const IconData check_box = IconData(0xe917, fontFamily: 'GalleryIcons');
  static const IconData cards = IconData(0xe918, fontFamily: 'GalleryIcons');
  static const IconData buttons = IconData(0xe919, fontFamily: 'GalleryIcons');
  static const IconData bottom_sheets = IconData(0xe91a, fontFamily: 'GalleryIcons');
  static const IconData bottom_navigation = IconData(0xe91b, fontFamily: 'GalleryIcons');
  static const IconData animation = IconData(0xe91c, fontFamily: 'GalleryIcons');
  static const IconData account_box = IconData(0xe91d, fontFamily: 'GalleryIcons');
  static const IconData snackbar = IconData(0xe91e, fontFamily: 'GalleryIcons');
  static const IconData category_mdc = IconData(0xe91f, fontFamily: 'GalleryIcons');
  static const IconData cupertino_progress = IconData(0xe920, fontFamily: 'GalleryIcons');
  static const IconData cupertino_pull_to_refresh = IconData(0xe921, fontFamily: 'GalleryIcons');
  static const IconData cupertino_switch = IconData(0xe922, fontFamily: 'GalleryIcons');
  static const IconData generic_buttons = IconData(0xe923, fontFamily: 'GalleryIcons');
  static const IconData backdrop = IconData(0xe924, fontFamily: 'GalleryIcons');
  static const IconData bottom_app_bar = IconData(0xe925, fontFamily: 'GalleryIcons');
  static const IconData bottom_sheet_persistent = IconData(0xe926, fontFamily: 'GalleryIcons');
  static const IconData lists_leave_behind = IconData(0xe927, fontFamily: 'GalleryIcons');
}
