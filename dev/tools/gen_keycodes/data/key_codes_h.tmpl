// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef FLUTTER_SHELL_PLATFORM_EMBEDDER_TEST_UTILS_KEY_CODES_G_H_
#define FLUTTER_SHELL_PLATFORM_EMBEDDER_TEST_UTILS_KEY_CODES_G_H_

#include <cinttypes>

// DO NOT EDIT -- DO NOT EDIT -- DO NOT EDIT
// This file is generated by
// flutter/flutter:dev/tools/gen_keycodes/bin/gen_keycodes.dart and should not
// be edited directly.
//
// Edit the template
// flutter/flutter:dev/tools/gen_keycodes/data/key_codes_cc.tmpl
// instead.
//
// See flutter/flutter:dev/tools/gen_keycodes/README.md for more information.

// This file contains keyboard constants to be used in unit tests.  They should
// not be used in production code.

namespace flutter {

namespace testing {

namespace keycodes {

@@@PHYSICAL_KEY_DEFINITIONS@@@

@@@LOGICAL_KEY_DEFINITIONS@@@

}  // namespace keycodes

}  // namespace testing

}  // namespace flutter

#endif  // FLUTTER_SHELL_PLATFORM_EMBEDDER_TEST_UTILS_KEY_CODES_G_H_
