
import 'package:flower_timemachine/controller/nitification_manager.dart';
import 'package:flower_timemachine/models/flower_notification_model.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

class RemindManagerController extends StateNotifier<AsyncValue<List<FlowerNotificationModel>>> {
  static const _limit = 50;

  bool _hasMore = true;

  bool _isLoading = false;

  RemindManagerController() : super(const AsyncValue.loading()) {
    loadMore();
  }

  void loadMore() async {
    if (_isLoading) {
      return;
    }
    _isLoading = true;

    final data = state.value ?? [];

    final newData = await FlowerNotificationModel.get(_limit, data.length);

    _hasMore = newData.length == _limit;

    state = AsyncValue.data(
      [...data, ...newData]
    );

    _isLoading = false;
  }

  Future<void> cancel(FlowerNotificationModel model) async {
    await (await NotificationManger.instance).update(
        model.flowerId,
        DateTime.fromMillisecondsSinceEpoch(model.alarmTime * 1000),
        null
    );

    final oldData = state.value ?? [];

    state = AsyncValue.data(
        [
          for (final item in oldData)
            if (item.flowerId != model.flowerId || item.alarmTime != model.alarmTime)
              item
        ]
    );
  }

  bool get hasMore => _hasMore;
}


final remindManagerControllerProvider = StateNotifierProvider.autoDispose<
    RemindManagerController,
    AsyncValue<List<FlowerNotificationModel>>
> ((ref) {
  return RemindManagerController();
});