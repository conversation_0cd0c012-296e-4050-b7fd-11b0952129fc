import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/resources.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/models/flower_notification_model.dart';
import 'package:flower_timemachine/pages/remind_help/remind_help.dart';
import 'package:flower_timemachine/widgets/error_reporter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:pull_down_button/pull_down_button.dart';

import 'controller/remind_manager_controller.dart';

class RemindManager extends ConsumerStatefulWidget {
  const RemindManager({super.key});

  static const String routeName = 'remind_manager';

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _RemindManagerState();
}

class _RemindManagerState extends ConsumerState {
  static final DateFormat _dateFormat = DateFormat.yMMMd();
  static final DateFormat _timeFormat = DateFormat.Hm();

  @override
  Widget build(BuildContext context) {
    final controller = ref.read(remindManagerControllerProvider.notifier);
    final provider = ref.watch(remindManagerControllerProvider);

    final locale = context.locale;
    final isChina = (locale.languageCode == "zh" && locale.scriptCode == "Hans");

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        title: const Text('remind_manager.title').tr(),
        centerTitle: true,
        actions: [
          if (isChina)
            IconButton(
              icon: const Icon(Icons.help_outline),
              onPressed: () {
                Navigator.pushNamed(context, RemindHelp.routeName);
              },
            )
        ],
      ),
      body: provider.when(
        loading: () => const Center(child: SizedBox(height: 100, width: 100, child: CircularProgressIndicator())),
        error: (error, stackTrace) => ErrorReporter(error: error, stackTrace: stackTrace),
        data: (items) => items.isEmpty
            ? Center(child: const Text('remind_manager.no_remind', style: TextStyle(color: Colors.black38)).tr())
            : Padding(
                padding: const EdgeInsets.all(10),
                child: ListView.builder(
                  itemBuilder: (content, index) {
                    if (index >= items.length) {
                      if (controller.hasMore) {
                        controller.loadMore();
                      }
                      return null;
                    }

                    final item = items[index];
                    final itemTime = DateTime.fromMillisecondsSinceEpoch(item.alarmTime * 1000);
                    final remindTime = "${_dateFormat.format(itemTime)} ${_timeFormat.format(itemTime)}";

                    return Column(children: [
                      _RemindManagerPullDownButton(
                          itemBuilder: (context) => menuButtons(context, item),
                          child: ListTile(
                            title: Padding(
                                padding: const EdgeInsets.symmetric(vertical: 5),
                                child: Text(item.flowerName, style: TextStyle(color: Theme.of(context).primaryColor))),
                            subtitle: Text(
                              remindTime,
                              style: const TextStyle(color: Color(0xFFFFB000)),
                            ),
                            leading: item.flowerAvatar == null
                                ? Image.asset(R.iconFlower, height: 65, width: 65)
                                : Image.file(File(Flower.getAvatarFullPath(item.flowerAvatar!)), height: 65, width: 65),
                          )),
                      const Divider(color: Colors.black38)
                    ]);
                  },
                ),
              ),
      ),
    );
  }

  List<PullDownMenuEntry> menuButtons(BuildContext context, FlowerNotificationModel model) {
    return [
      PullDownMenuItem(
          title: 'remind_manager.cancel'.tr(),
          itemTheme: const PullDownMenuItemTheme(textStyle: TextStyle(color: Colors.red)),
          iconColor: Colors.red,
          icon: Icons.delete,
          onTap: () => onCancelRemind(model))
    ];
  }

  void onCancelRemind(FlowerNotificationModel model) async {
    final controller = ref.read(remindManagerControllerProvider.notifier);
    await controller.cancel(model);

    Fluttertoast.showToast(msg: "remind_manager.cancel_success".tr(), toastLength: Toast.LENGTH_LONG);
  }
}

class _RemindManagerPullDownButton extends StatelessWidget {
  final PullDownMenuItemBuilder itemBuilder;
  final Widget child;

  const _RemindManagerPullDownButton({required this.itemBuilder, required this.child});

  @override
  Widget build(BuildContext context) {
    return PullDownButton(
        itemBuilder: itemBuilder,
        buttonBuilder: (context, showMenu) => InkWell(
              onLongPress: showMenu,
              child: child,
            ));
  }
}
