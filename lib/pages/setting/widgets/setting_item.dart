import 'package:flutter/material.dart';

class SettingItem extends StatelessWidget {
  final Widget icon;
  final String title;
  final VoidCallback? callback;
  final EdgeInsetsGeometry padding;
  final Widget? right;

  const SettingItem(
      {super.key, required this.icon, required this.title, required this.padding, this.right, this.callback});

  @override
  Widget build(BuildContext context) {
    final rows = [icon, const SizedBox(width: 5), Text(title, style: const TextStyle(fontSize: 18))];

    if (right != null) {
      rows.addAll([const Spacer(), right!]);
    }

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: callback,
      child: Padding(
        padding: padding,
        child: Row(children: rows),
      ),
    );
  }
}

class SettingDivider extends StatelessWidget {
  const SettingDivider({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(width: 1, color: Colors.black12),
      ),
    );
  }
}
