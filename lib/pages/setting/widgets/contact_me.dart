import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';

import 'setting_item.dart';

class ContactMeWidget extends StatelessWidget {
  const ContactMeWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const ContactMeEmailWidget();
  }
}

class ContactMeWechatWidget extends StatelessWidget {
  const ContactMeWechatWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SettingItem(
      icon: const Icon(Icons.wechat, size: 27),
      title: 'setting_page.contact_me'.tr(),
      right: const Text('RememberMe-001', style: TextStyle(fontSize: 18)),
      padding: const EdgeInsets.symmetric(vertical: 15),
      callback: onContactMe,
    );
  }

  void onContactMe() async {
    await Clipboard.setData(const ClipboardData(text: "RememberMe-001"));
    Fluttertoast.showToast(msg: "已复制到粘贴板，请到微信搜索添加好友", toastLength: Toast.LENGTH_SHORT);
  }
}

class ContactMeEmailWidget extends StatelessWidget {
  const ContactMeEmailWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SettingItem(
      icon: const Icon(Icons.email_outlined, size: 27),
      title: 'setting_page.contact_me'.tr(),
      right: const Text('<EMAIL>', style: TextStyle(fontSize: 18)),
      padding: const EdgeInsets.symmetric(vertical: 15),
      callback: onContactMe,
    );
  }

  void onContactMe() async {
    await Clipboard.setData(const ClipboardData(text: "<EMAIL>"));
    Fluttertoast.showToast(msg: "setting_page.copy_email".tr(), toastLength: Toast.LENGTH_SHORT);
  }
}