
import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AddNurtureTypeController extends ChangeNotifier {
  final NurtureType? nurtureType;

  String? _newIcon;
  int? _newCycle;
  String? _newName;

  bool _isChange = false;

  final nurtureTypeIcons = [
    "icons/water.svg",
    "icons/fertilizer.svg",
    "icons/cut.svg",
    "icons/pest_control.svg",
    "icons/water1.svg",
    "icons/spray.svg",
    "icons/shovel.svg",
    "icons/flower1.svg",
  ];

  AddNurtureTypeController(this.nurtureType);

  Future<NurtureType> save() async {
    if (nurtureType != null) {
      await nurtureType!.update(
        name: _newName,
        icon: icon,
        defaultCycle: cycle
      );

      return nurtureType!;
    } else {
      return await NurtureType.create(
        name: _newName!,
        icon: icon,
        defaultCycle: _newCycle ?? 0,
        enable: true
      );
    }
  }

  String get title {
    if (nurtureType == null) {
      return "add_nurture.title_new".tr();
    } else {
      return "add_nurture.title_edit".tr();
    }
  }

  int get cycle => _newCycle ?? nurtureType?.defaultCycle ?? 0;

  set cycle(int value) {
    if (value != cycle) {
      _newCycle = value;
      _isChange = true;
      notifyListeners();
    }
  }

  String get icon => _newIcon ?? nurtureType?.icon ?? nurtureTypeIcons[0];

  set icon(String newValue) {
    if (newValue != icon) {
      _newIcon = newValue;
      _isChange = true;
      notifyListeners();
    }
  }

  String? get name => _newName ?? nurtureType?.name;

  set name(String? value) {
    if ((value ?? "") != (nurtureType?.name ?? "")) {
      _isChange = true;
      _newName = value;
    }
  }

  bool get isChange => _isChange;
}

final addNurtureTypeControllerProvider = ChangeNotifierProvider.autoDispose.family<
    AddNurtureTypeController,
    NurtureType?
>((ref, type) => AddNurtureTypeController(type));