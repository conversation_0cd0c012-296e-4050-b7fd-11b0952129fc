// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'backup_controller.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BackupState _$BackupStateFromJson(Map<String, dynamic> json) {
  switch (json['runtimeType']) {
    case 'noBackup':
      return BackupStateNoBackUp.fromJson(json);
    case 'uploaded':
      return BackupStateUploaded.fromJson(json);
    case 'uploading':
      return BackupStateUploading.fromJson(json);
    case 'downloaded':
      return BackupStateDownloaded.fromJson(json);
    case 'downloading':
      return BackupStateDownloading.fromJson(json);
    case 'restored':
      return BackupStateRestored.fromJson(json);
    case 'unready':
      return BackupStateUnready.fromJson(json);

    default:
      throw CheckedFromJsonException(json, 'runtimeType', 'BackupState',
          'Invalid union type "${json['runtimeType']}"!');
  }
}

/// @nodoc
mixin _$BackupState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime? downloadDate) noBackup,
    required TResult Function(DateTime lastBackupDate) uploaded,
    required TResult Function(DateTime startDate, int progress) uploading,
    required TResult Function(DateTime lastDate) downloaded,
    required TResult Function(int progress) downloading,
    required TResult Function(DateTime lastDate) restored,
    required TResult Function() unready,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime? downloadDate)? noBackup,
    TResult? Function(DateTime lastBackupDate)? uploaded,
    TResult? Function(DateTime startDate, int progress)? uploading,
    TResult? Function(DateTime lastDate)? downloaded,
    TResult? Function(int progress)? downloading,
    TResult? Function(DateTime lastDate)? restored,
    TResult? Function()? unready,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime? downloadDate)? noBackup,
    TResult Function(DateTime lastBackupDate)? uploaded,
    TResult Function(DateTime startDate, int progress)? uploading,
    TResult Function(DateTime lastDate)? downloaded,
    TResult Function(int progress)? downloading,
    TResult Function(DateTime lastDate)? restored,
    TResult Function()? unready,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BackupStateNoBackUp value) noBackup,
    required TResult Function(BackupStateUploaded value) uploaded,
    required TResult Function(BackupStateUploading value) uploading,
    required TResult Function(BackupStateDownloaded value) downloaded,
    required TResult Function(BackupStateDownloading value) downloading,
    required TResult Function(BackupStateRestored value) restored,
    required TResult Function(BackupStateUnready value) unready,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BackupStateNoBackUp value)? noBackup,
    TResult? Function(BackupStateUploaded value)? uploaded,
    TResult? Function(BackupStateUploading value)? uploading,
    TResult? Function(BackupStateDownloaded value)? downloaded,
    TResult? Function(BackupStateDownloading value)? downloading,
    TResult? Function(BackupStateRestored value)? restored,
    TResult? Function(BackupStateUnready value)? unready,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BackupStateNoBackUp value)? noBackup,
    TResult Function(BackupStateUploaded value)? uploaded,
    TResult Function(BackupStateUploading value)? uploading,
    TResult Function(BackupStateDownloaded value)? downloaded,
    TResult Function(BackupStateDownloading value)? downloading,
    TResult Function(BackupStateRestored value)? restored,
    TResult Function(BackupStateUnready value)? unready,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Serializes this BackupState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BackupStateCopyWith<$Res> {
  factory $BackupStateCopyWith(
          BackupState value, $Res Function(BackupState) then) =
      _$BackupStateCopyWithImpl<$Res, BackupState>;
}

/// @nodoc
class _$BackupStateCopyWithImpl<$Res, $Val extends BackupState>
    implements $BackupStateCopyWith<$Res> {
  _$BackupStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BackupState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$BackupStateNoBackUpImplCopyWith<$Res> {
  factory _$$BackupStateNoBackUpImplCopyWith(_$BackupStateNoBackUpImpl value,
          $Res Function(_$BackupStateNoBackUpImpl) then) =
      __$$BackupStateNoBackUpImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime? downloadDate});
}

/// @nodoc
class __$$BackupStateNoBackUpImplCopyWithImpl<$Res>
    extends _$BackupStateCopyWithImpl<$Res, _$BackupStateNoBackUpImpl>
    implements _$$BackupStateNoBackUpImplCopyWith<$Res> {
  __$$BackupStateNoBackUpImplCopyWithImpl(_$BackupStateNoBackUpImpl _value,
      $Res Function(_$BackupStateNoBackUpImpl) _then)
      : super(_value, _then);

  /// Create a copy of BackupState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? downloadDate = freezed,
  }) {
    return _then(_$BackupStateNoBackUpImpl(
      freezed == downloadDate
          ? _value.downloadDate
          : downloadDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BackupStateNoBackUpImpl implements BackupStateNoBackUp {
  const _$BackupStateNoBackUpImpl(this.downloadDate, {final String? $type})
      : $type = $type ?? 'noBackup';

  factory _$BackupStateNoBackUpImpl.fromJson(Map<String, dynamic> json) =>
      _$$BackupStateNoBackUpImplFromJson(json);

  @override
  final DateTime? downloadDate;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'BackupState.noBackup(downloadDate: $downloadDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BackupStateNoBackUpImpl &&
            (identical(other.downloadDate, downloadDate) ||
                other.downloadDate == downloadDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, downloadDate);

  /// Create a copy of BackupState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BackupStateNoBackUpImplCopyWith<_$BackupStateNoBackUpImpl> get copyWith =>
      __$$BackupStateNoBackUpImplCopyWithImpl<_$BackupStateNoBackUpImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime? downloadDate) noBackup,
    required TResult Function(DateTime lastBackupDate) uploaded,
    required TResult Function(DateTime startDate, int progress) uploading,
    required TResult Function(DateTime lastDate) downloaded,
    required TResult Function(int progress) downloading,
    required TResult Function(DateTime lastDate) restored,
    required TResult Function() unready,
  }) {
    return noBackup(downloadDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime? downloadDate)? noBackup,
    TResult? Function(DateTime lastBackupDate)? uploaded,
    TResult? Function(DateTime startDate, int progress)? uploading,
    TResult? Function(DateTime lastDate)? downloaded,
    TResult? Function(int progress)? downloading,
    TResult? Function(DateTime lastDate)? restored,
    TResult? Function()? unready,
  }) {
    return noBackup?.call(downloadDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime? downloadDate)? noBackup,
    TResult Function(DateTime lastBackupDate)? uploaded,
    TResult Function(DateTime startDate, int progress)? uploading,
    TResult Function(DateTime lastDate)? downloaded,
    TResult Function(int progress)? downloading,
    TResult Function(DateTime lastDate)? restored,
    TResult Function()? unready,
    required TResult orElse(),
  }) {
    if (noBackup != null) {
      return noBackup(downloadDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BackupStateNoBackUp value) noBackup,
    required TResult Function(BackupStateUploaded value) uploaded,
    required TResult Function(BackupStateUploading value) uploading,
    required TResult Function(BackupStateDownloaded value) downloaded,
    required TResult Function(BackupStateDownloading value) downloading,
    required TResult Function(BackupStateRestored value) restored,
    required TResult Function(BackupStateUnready value) unready,
  }) {
    return noBackup(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BackupStateNoBackUp value)? noBackup,
    TResult? Function(BackupStateUploaded value)? uploaded,
    TResult? Function(BackupStateUploading value)? uploading,
    TResult? Function(BackupStateDownloaded value)? downloaded,
    TResult? Function(BackupStateDownloading value)? downloading,
    TResult? Function(BackupStateRestored value)? restored,
    TResult? Function(BackupStateUnready value)? unready,
  }) {
    return noBackup?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BackupStateNoBackUp value)? noBackup,
    TResult Function(BackupStateUploaded value)? uploaded,
    TResult Function(BackupStateUploading value)? uploading,
    TResult Function(BackupStateDownloaded value)? downloaded,
    TResult Function(BackupStateDownloading value)? downloading,
    TResult Function(BackupStateRestored value)? restored,
    TResult Function(BackupStateUnready value)? unready,
    required TResult orElse(),
  }) {
    if (noBackup != null) {
      return noBackup(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$BackupStateNoBackUpImplToJson(
      this,
    );
  }
}

abstract class BackupStateNoBackUp implements BackupState {
  const factory BackupStateNoBackUp(final DateTime? downloadDate) =
      _$BackupStateNoBackUpImpl;

  factory BackupStateNoBackUp.fromJson(Map<String, dynamic> json) =
      _$BackupStateNoBackUpImpl.fromJson;

  DateTime? get downloadDate;

  /// Create a copy of BackupState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BackupStateNoBackUpImplCopyWith<_$BackupStateNoBackUpImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BackupStateUploadedImplCopyWith<$Res> {
  factory _$$BackupStateUploadedImplCopyWith(_$BackupStateUploadedImpl value,
          $Res Function(_$BackupStateUploadedImpl) then) =
      __$$BackupStateUploadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime lastBackupDate});
}

/// @nodoc
class __$$BackupStateUploadedImplCopyWithImpl<$Res>
    extends _$BackupStateCopyWithImpl<$Res, _$BackupStateUploadedImpl>
    implements _$$BackupStateUploadedImplCopyWith<$Res> {
  __$$BackupStateUploadedImplCopyWithImpl(_$BackupStateUploadedImpl _value,
      $Res Function(_$BackupStateUploadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BackupState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lastBackupDate = null,
  }) {
    return _then(_$BackupStateUploadedImpl(
      null == lastBackupDate
          ? _value.lastBackupDate
          : lastBackupDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BackupStateUploadedImpl implements BackupStateUploaded {
  const _$BackupStateUploadedImpl(this.lastBackupDate, {final String? $type})
      : $type = $type ?? 'uploaded';

  factory _$BackupStateUploadedImpl.fromJson(Map<String, dynamic> json) =>
      _$$BackupStateUploadedImplFromJson(json);

  @override
  final DateTime lastBackupDate;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'BackupState.uploaded(lastBackupDate: $lastBackupDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BackupStateUploadedImpl &&
            (identical(other.lastBackupDate, lastBackupDate) ||
                other.lastBackupDate == lastBackupDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, lastBackupDate);

  /// Create a copy of BackupState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BackupStateUploadedImplCopyWith<_$BackupStateUploadedImpl> get copyWith =>
      __$$BackupStateUploadedImplCopyWithImpl<_$BackupStateUploadedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime? downloadDate) noBackup,
    required TResult Function(DateTime lastBackupDate) uploaded,
    required TResult Function(DateTime startDate, int progress) uploading,
    required TResult Function(DateTime lastDate) downloaded,
    required TResult Function(int progress) downloading,
    required TResult Function(DateTime lastDate) restored,
    required TResult Function() unready,
  }) {
    return uploaded(lastBackupDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime? downloadDate)? noBackup,
    TResult? Function(DateTime lastBackupDate)? uploaded,
    TResult? Function(DateTime startDate, int progress)? uploading,
    TResult? Function(DateTime lastDate)? downloaded,
    TResult? Function(int progress)? downloading,
    TResult? Function(DateTime lastDate)? restored,
    TResult? Function()? unready,
  }) {
    return uploaded?.call(lastBackupDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime? downloadDate)? noBackup,
    TResult Function(DateTime lastBackupDate)? uploaded,
    TResult Function(DateTime startDate, int progress)? uploading,
    TResult Function(DateTime lastDate)? downloaded,
    TResult Function(int progress)? downloading,
    TResult Function(DateTime lastDate)? restored,
    TResult Function()? unready,
    required TResult orElse(),
  }) {
    if (uploaded != null) {
      return uploaded(lastBackupDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BackupStateNoBackUp value) noBackup,
    required TResult Function(BackupStateUploaded value) uploaded,
    required TResult Function(BackupStateUploading value) uploading,
    required TResult Function(BackupStateDownloaded value) downloaded,
    required TResult Function(BackupStateDownloading value) downloading,
    required TResult Function(BackupStateRestored value) restored,
    required TResult Function(BackupStateUnready value) unready,
  }) {
    return uploaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BackupStateNoBackUp value)? noBackup,
    TResult? Function(BackupStateUploaded value)? uploaded,
    TResult? Function(BackupStateUploading value)? uploading,
    TResult? Function(BackupStateDownloaded value)? downloaded,
    TResult? Function(BackupStateDownloading value)? downloading,
    TResult? Function(BackupStateRestored value)? restored,
    TResult? Function(BackupStateUnready value)? unready,
  }) {
    return uploaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BackupStateNoBackUp value)? noBackup,
    TResult Function(BackupStateUploaded value)? uploaded,
    TResult Function(BackupStateUploading value)? uploading,
    TResult Function(BackupStateDownloaded value)? downloaded,
    TResult Function(BackupStateDownloading value)? downloading,
    TResult Function(BackupStateRestored value)? restored,
    TResult Function(BackupStateUnready value)? unready,
    required TResult orElse(),
  }) {
    if (uploaded != null) {
      return uploaded(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$BackupStateUploadedImplToJson(
      this,
    );
  }
}

abstract class BackupStateUploaded implements BackupState {
  const factory BackupStateUploaded(final DateTime lastBackupDate) =
      _$BackupStateUploadedImpl;

  factory BackupStateUploaded.fromJson(Map<String, dynamic> json) =
      _$BackupStateUploadedImpl.fromJson;

  DateTime get lastBackupDate;

  /// Create a copy of BackupState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BackupStateUploadedImplCopyWith<_$BackupStateUploadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BackupStateUploadingImplCopyWith<$Res> {
  factory _$$BackupStateUploadingImplCopyWith(_$BackupStateUploadingImpl value,
          $Res Function(_$BackupStateUploadingImpl) then) =
      __$$BackupStateUploadingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime startDate, int progress});
}

/// @nodoc
class __$$BackupStateUploadingImplCopyWithImpl<$Res>
    extends _$BackupStateCopyWithImpl<$Res, _$BackupStateUploadingImpl>
    implements _$$BackupStateUploadingImplCopyWith<$Res> {
  __$$BackupStateUploadingImplCopyWithImpl(_$BackupStateUploadingImpl _value,
      $Res Function(_$BackupStateUploadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of BackupState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? startDate = null,
    Object? progress = null,
  }) {
    return _then(_$BackupStateUploadingImpl(
      null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      null == progress
          ? _value.progress
          : progress // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BackupStateUploadingImpl implements BackupStateUploading {
  const _$BackupStateUploadingImpl(this.startDate, this.progress,
      {final String? $type})
      : $type = $type ?? 'uploading';

  factory _$BackupStateUploadingImpl.fromJson(Map<String, dynamic> json) =>
      _$$BackupStateUploadingImplFromJson(json);

  @override
  final DateTime startDate;
  @override
  final int progress;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'BackupState.uploading(startDate: $startDate, progress: $progress)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BackupStateUploadingImpl &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.progress, progress) ||
                other.progress == progress));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, startDate, progress);

  /// Create a copy of BackupState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BackupStateUploadingImplCopyWith<_$BackupStateUploadingImpl>
      get copyWith =>
          __$$BackupStateUploadingImplCopyWithImpl<_$BackupStateUploadingImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime? downloadDate) noBackup,
    required TResult Function(DateTime lastBackupDate) uploaded,
    required TResult Function(DateTime startDate, int progress) uploading,
    required TResult Function(DateTime lastDate) downloaded,
    required TResult Function(int progress) downloading,
    required TResult Function(DateTime lastDate) restored,
    required TResult Function() unready,
  }) {
    return uploading(startDate, progress);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime? downloadDate)? noBackup,
    TResult? Function(DateTime lastBackupDate)? uploaded,
    TResult? Function(DateTime startDate, int progress)? uploading,
    TResult? Function(DateTime lastDate)? downloaded,
    TResult? Function(int progress)? downloading,
    TResult? Function(DateTime lastDate)? restored,
    TResult? Function()? unready,
  }) {
    return uploading?.call(startDate, progress);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime? downloadDate)? noBackup,
    TResult Function(DateTime lastBackupDate)? uploaded,
    TResult Function(DateTime startDate, int progress)? uploading,
    TResult Function(DateTime lastDate)? downloaded,
    TResult Function(int progress)? downloading,
    TResult Function(DateTime lastDate)? restored,
    TResult Function()? unready,
    required TResult orElse(),
  }) {
    if (uploading != null) {
      return uploading(startDate, progress);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BackupStateNoBackUp value) noBackup,
    required TResult Function(BackupStateUploaded value) uploaded,
    required TResult Function(BackupStateUploading value) uploading,
    required TResult Function(BackupStateDownloaded value) downloaded,
    required TResult Function(BackupStateDownloading value) downloading,
    required TResult Function(BackupStateRestored value) restored,
    required TResult Function(BackupStateUnready value) unready,
  }) {
    return uploading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BackupStateNoBackUp value)? noBackup,
    TResult? Function(BackupStateUploaded value)? uploaded,
    TResult? Function(BackupStateUploading value)? uploading,
    TResult? Function(BackupStateDownloaded value)? downloaded,
    TResult? Function(BackupStateDownloading value)? downloading,
    TResult? Function(BackupStateRestored value)? restored,
    TResult? Function(BackupStateUnready value)? unready,
  }) {
    return uploading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BackupStateNoBackUp value)? noBackup,
    TResult Function(BackupStateUploaded value)? uploaded,
    TResult Function(BackupStateUploading value)? uploading,
    TResult Function(BackupStateDownloaded value)? downloaded,
    TResult Function(BackupStateDownloading value)? downloading,
    TResult Function(BackupStateRestored value)? restored,
    TResult Function(BackupStateUnready value)? unready,
    required TResult orElse(),
  }) {
    if (uploading != null) {
      return uploading(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$BackupStateUploadingImplToJson(
      this,
    );
  }
}

abstract class BackupStateUploading implements BackupState {
  const factory BackupStateUploading(
          final DateTime startDate, final int progress) =
      _$BackupStateUploadingImpl;

  factory BackupStateUploading.fromJson(Map<String, dynamic> json) =
      _$BackupStateUploadingImpl.fromJson;

  DateTime get startDate;
  int get progress;

  /// Create a copy of BackupState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BackupStateUploadingImplCopyWith<_$BackupStateUploadingImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BackupStateDownloadedImplCopyWith<$Res> {
  factory _$$BackupStateDownloadedImplCopyWith(
          _$BackupStateDownloadedImpl value,
          $Res Function(_$BackupStateDownloadedImpl) then) =
      __$$BackupStateDownloadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime lastDate});
}

/// @nodoc
class __$$BackupStateDownloadedImplCopyWithImpl<$Res>
    extends _$BackupStateCopyWithImpl<$Res, _$BackupStateDownloadedImpl>
    implements _$$BackupStateDownloadedImplCopyWith<$Res> {
  __$$BackupStateDownloadedImplCopyWithImpl(_$BackupStateDownloadedImpl _value,
      $Res Function(_$BackupStateDownloadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BackupState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lastDate = null,
  }) {
    return _then(_$BackupStateDownloadedImpl(
      null == lastDate
          ? _value.lastDate
          : lastDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BackupStateDownloadedImpl implements BackupStateDownloaded {
  const _$BackupStateDownloadedImpl(this.lastDate, {final String? $type})
      : $type = $type ?? 'downloaded';

  factory _$BackupStateDownloadedImpl.fromJson(Map<String, dynamic> json) =>
      _$$BackupStateDownloadedImplFromJson(json);

  @override
  final DateTime lastDate;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'BackupState.downloaded(lastDate: $lastDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BackupStateDownloadedImpl &&
            (identical(other.lastDate, lastDate) ||
                other.lastDate == lastDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, lastDate);

  /// Create a copy of BackupState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BackupStateDownloadedImplCopyWith<_$BackupStateDownloadedImpl>
      get copyWith => __$$BackupStateDownloadedImplCopyWithImpl<
          _$BackupStateDownloadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime? downloadDate) noBackup,
    required TResult Function(DateTime lastBackupDate) uploaded,
    required TResult Function(DateTime startDate, int progress) uploading,
    required TResult Function(DateTime lastDate) downloaded,
    required TResult Function(int progress) downloading,
    required TResult Function(DateTime lastDate) restored,
    required TResult Function() unready,
  }) {
    return downloaded(lastDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime? downloadDate)? noBackup,
    TResult? Function(DateTime lastBackupDate)? uploaded,
    TResult? Function(DateTime startDate, int progress)? uploading,
    TResult? Function(DateTime lastDate)? downloaded,
    TResult? Function(int progress)? downloading,
    TResult? Function(DateTime lastDate)? restored,
    TResult? Function()? unready,
  }) {
    return downloaded?.call(lastDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime? downloadDate)? noBackup,
    TResult Function(DateTime lastBackupDate)? uploaded,
    TResult Function(DateTime startDate, int progress)? uploading,
    TResult Function(DateTime lastDate)? downloaded,
    TResult Function(int progress)? downloading,
    TResult Function(DateTime lastDate)? restored,
    TResult Function()? unready,
    required TResult orElse(),
  }) {
    if (downloaded != null) {
      return downloaded(lastDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BackupStateNoBackUp value) noBackup,
    required TResult Function(BackupStateUploaded value) uploaded,
    required TResult Function(BackupStateUploading value) uploading,
    required TResult Function(BackupStateDownloaded value) downloaded,
    required TResult Function(BackupStateDownloading value) downloading,
    required TResult Function(BackupStateRestored value) restored,
    required TResult Function(BackupStateUnready value) unready,
  }) {
    return downloaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BackupStateNoBackUp value)? noBackup,
    TResult? Function(BackupStateUploaded value)? uploaded,
    TResult? Function(BackupStateUploading value)? uploading,
    TResult? Function(BackupStateDownloaded value)? downloaded,
    TResult? Function(BackupStateDownloading value)? downloading,
    TResult? Function(BackupStateRestored value)? restored,
    TResult? Function(BackupStateUnready value)? unready,
  }) {
    return downloaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BackupStateNoBackUp value)? noBackup,
    TResult Function(BackupStateUploaded value)? uploaded,
    TResult Function(BackupStateUploading value)? uploading,
    TResult Function(BackupStateDownloaded value)? downloaded,
    TResult Function(BackupStateDownloading value)? downloading,
    TResult Function(BackupStateRestored value)? restored,
    TResult Function(BackupStateUnready value)? unready,
    required TResult orElse(),
  }) {
    if (downloaded != null) {
      return downloaded(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$BackupStateDownloadedImplToJson(
      this,
    );
  }
}

abstract class BackupStateDownloaded implements BackupState {
  const factory BackupStateDownloaded(final DateTime lastDate) =
      _$BackupStateDownloadedImpl;

  factory BackupStateDownloaded.fromJson(Map<String, dynamic> json) =
      _$BackupStateDownloadedImpl.fromJson;

  DateTime get lastDate;

  /// Create a copy of BackupState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BackupStateDownloadedImplCopyWith<_$BackupStateDownloadedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BackupStateDownloadingImplCopyWith<$Res> {
  factory _$$BackupStateDownloadingImplCopyWith(
          _$BackupStateDownloadingImpl value,
          $Res Function(_$BackupStateDownloadingImpl) then) =
      __$$BackupStateDownloadingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int progress});
}

/// @nodoc
class __$$BackupStateDownloadingImplCopyWithImpl<$Res>
    extends _$BackupStateCopyWithImpl<$Res, _$BackupStateDownloadingImpl>
    implements _$$BackupStateDownloadingImplCopyWith<$Res> {
  __$$BackupStateDownloadingImplCopyWithImpl(
      _$BackupStateDownloadingImpl _value,
      $Res Function(_$BackupStateDownloadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of BackupState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? progress = null,
  }) {
    return _then(_$BackupStateDownloadingImpl(
      null == progress
          ? _value.progress
          : progress // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BackupStateDownloadingImpl implements BackupStateDownloading {
  const _$BackupStateDownloadingImpl(this.progress, {final String? $type})
      : $type = $type ?? 'downloading';

  factory _$BackupStateDownloadingImpl.fromJson(Map<String, dynamic> json) =>
      _$$BackupStateDownloadingImplFromJson(json);

  @override
  final int progress;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'BackupState.downloading(progress: $progress)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BackupStateDownloadingImpl &&
            (identical(other.progress, progress) ||
                other.progress == progress));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, progress);

  /// Create a copy of BackupState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BackupStateDownloadingImplCopyWith<_$BackupStateDownloadingImpl>
      get copyWith => __$$BackupStateDownloadingImplCopyWithImpl<
          _$BackupStateDownloadingImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime? downloadDate) noBackup,
    required TResult Function(DateTime lastBackupDate) uploaded,
    required TResult Function(DateTime startDate, int progress) uploading,
    required TResult Function(DateTime lastDate) downloaded,
    required TResult Function(int progress) downloading,
    required TResult Function(DateTime lastDate) restored,
    required TResult Function() unready,
  }) {
    return downloading(progress);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime? downloadDate)? noBackup,
    TResult? Function(DateTime lastBackupDate)? uploaded,
    TResult? Function(DateTime startDate, int progress)? uploading,
    TResult? Function(DateTime lastDate)? downloaded,
    TResult? Function(int progress)? downloading,
    TResult? Function(DateTime lastDate)? restored,
    TResult? Function()? unready,
  }) {
    return downloading?.call(progress);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime? downloadDate)? noBackup,
    TResult Function(DateTime lastBackupDate)? uploaded,
    TResult Function(DateTime startDate, int progress)? uploading,
    TResult Function(DateTime lastDate)? downloaded,
    TResult Function(int progress)? downloading,
    TResult Function(DateTime lastDate)? restored,
    TResult Function()? unready,
    required TResult orElse(),
  }) {
    if (downloading != null) {
      return downloading(progress);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BackupStateNoBackUp value) noBackup,
    required TResult Function(BackupStateUploaded value) uploaded,
    required TResult Function(BackupStateUploading value) uploading,
    required TResult Function(BackupStateDownloaded value) downloaded,
    required TResult Function(BackupStateDownloading value) downloading,
    required TResult Function(BackupStateRestored value) restored,
    required TResult Function(BackupStateUnready value) unready,
  }) {
    return downloading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BackupStateNoBackUp value)? noBackup,
    TResult? Function(BackupStateUploaded value)? uploaded,
    TResult? Function(BackupStateUploading value)? uploading,
    TResult? Function(BackupStateDownloaded value)? downloaded,
    TResult? Function(BackupStateDownloading value)? downloading,
    TResult? Function(BackupStateRestored value)? restored,
    TResult? Function(BackupStateUnready value)? unready,
  }) {
    return downloading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BackupStateNoBackUp value)? noBackup,
    TResult Function(BackupStateUploaded value)? uploaded,
    TResult Function(BackupStateUploading value)? uploading,
    TResult Function(BackupStateDownloaded value)? downloaded,
    TResult Function(BackupStateDownloading value)? downloading,
    TResult Function(BackupStateRestored value)? restored,
    TResult Function(BackupStateUnready value)? unready,
    required TResult orElse(),
  }) {
    if (downloading != null) {
      return downloading(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$BackupStateDownloadingImplToJson(
      this,
    );
  }
}

abstract class BackupStateDownloading implements BackupState {
  const factory BackupStateDownloading(final int progress) =
      _$BackupStateDownloadingImpl;

  factory BackupStateDownloading.fromJson(Map<String, dynamic> json) =
      _$BackupStateDownloadingImpl.fromJson;

  int get progress;

  /// Create a copy of BackupState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BackupStateDownloadingImplCopyWith<_$BackupStateDownloadingImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BackupStateRestoredImplCopyWith<$Res> {
  factory _$$BackupStateRestoredImplCopyWith(_$BackupStateRestoredImpl value,
          $Res Function(_$BackupStateRestoredImpl) then) =
      __$$BackupStateRestoredImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime lastDate});
}

/// @nodoc
class __$$BackupStateRestoredImplCopyWithImpl<$Res>
    extends _$BackupStateCopyWithImpl<$Res, _$BackupStateRestoredImpl>
    implements _$$BackupStateRestoredImplCopyWith<$Res> {
  __$$BackupStateRestoredImplCopyWithImpl(_$BackupStateRestoredImpl _value,
      $Res Function(_$BackupStateRestoredImpl) _then)
      : super(_value, _then);

  /// Create a copy of BackupState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lastDate = null,
  }) {
    return _then(_$BackupStateRestoredImpl(
      null == lastDate
          ? _value.lastDate
          : lastDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BackupStateRestoredImpl implements BackupStateRestored {
  const _$BackupStateRestoredImpl(this.lastDate, {final String? $type})
      : $type = $type ?? 'restored';

  factory _$BackupStateRestoredImpl.fromJson(Map<String, dynamic> json) =>
      _$$BackupStateRestoredImplFromJson(json);

  @override
  final DateTime lastDate;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'BackupState.restored(lastDate: $lastDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BackupStateRestoredImpl &&
            (identical(other.lastDate, lastDate) ||
                other.lastDate == lastDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, lastDate);

  /// Create a copy of BackupState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BackupStateRestoredImplCopyWith<_$BackupStateRestoredImpl> get copyWith =>
      __$$BackupStateRestoredImplCopyWithImpl<_$BackupStateRestoredImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime? downloadDate) noBackup,
    required TResult Function(DateTime lastBackupDate) uploaded,
    required TResult Function(DateTime startDate, int progress) uploading,
    required TResult Function(DateTime lastDate) downloaded,
    required TResult Function(int progress) downloading,
    required TResult Function(DateTime lastDate) restored,
    required TResult Function() unready,
  }) {
    return restored(lastDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime? downloadDate)? noBackup,
    TResult? Function(DateTime lastBackupDate)? uploaded,
    TResult? Function(DateTime startDate, int progress)? uploading,
    TResult? Function(DateTime lastDate)? downloaded,
    TResult? Function(int progress)? downloading,
    TResult? Function(DateTime lastDate)? restored,
    TResult? Function()? unready,
  }) {
    return restored?.call(lastDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime? downloadDate)? noBackup,
    TResult Function(DateTime lastBackupDate)? uploaded,
    TResult Function(DateTime startDate, int progress)? uploading,
    TResult Function(DateTime lastDate)? downloaded,
    TResult Function(int progress)? downloading,
    TResult Function(DateTime lastDate)? restored,
    TResult Function()? unready,
    required TResult orElse(),
  }) {
    if (restored != null) {
      return restored(lastDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BackupStateNoBackUp value) noBackup,
    required TResult Function(BackupStateUploaded value) uploaded,
    required TResult Function(BackupStateUploading value) uploading,
    required TResult Function(BackupStateDownloaded value) downloaded,
    required TResult Function(BackupStateDownloading value) downloading,
    required TResult Function(BackupStateRestored value) restored,
    required TResult Function(BackupStateUnready value) unready,
  }) {
    return restored(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BackupStateNoBackUp value)? noBackup,
    TResult? Function(BackupStateUploaded value)? uploaded,
    TResult? Function(BackupStateUploading value)? uploading,
    TResult? Function(BackupStateDownloaded value)? downloaded,
    TResult? Function(BackupStateDownloading value)? downloading,
    TResult? Function(BackupStateRestored value)? restored,
    TResult? Function(BackupStateUnready value)? unready,
  }) {
    return restored?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BackupStateNoBackUp value)? noBackup,
    TResult Function(BackupStateUploaded value)? uploaded,
    TResult Function(BackupStateUploading value)? uploading,
    TResult Function(BackupStateDownloaded value)? downloaded,
    TResult Function(BackupStateDownloading value)? downloading,
    TResult Function(BackupStateRestored value)? restored,
    TResult Function(BackupStateUnready value)? unready,
    required TResult orElse(),
  }) {
    if (restored != null) {
      return restored(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$BackupStateRestoredImplToJson(
      this,
    );
  }
}

abstract class BackupStateRestored implements BackupState {
  const factory BackupStateRestored(final DateTime lastDate) =
      _$BackupStateRestoredImpl;

  factory BackupStateRestored.fromJson(Map<String, dynamic> json) =
      _$BackupStateRestoredImpl.fromJson;

  DateTime get lastDate;

  /// Create a copy of BackupState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BackupStateRestoredImplCopyWith<_$BackupStateRestoredImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BackupStateUnreadyImplCopyWith<$Res> {
  factory _$$BackupStateUnreadyImplCopyWith(_$BackupStateUnreadyImpl value,
          $Res Function(_$BackupStateUnreadyImpl) then) =
      __$$BackupStateUnreadyImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$BackupStateUnreadyImplCopyWithImpl<$Res>
    extends _$BackupStateCopyWithImpl<$Res, _$BackupStateUnreadyImpl>
    implements _$$BackupStateUnreadyImplCopyWith<$Res> {
  __$$BackupStateUnreadyImplCopyWithImpl(_$BackupStateUnreadyImpl _value,
      $Res Function(_$BackupStateUnreadyImpl) _then)
      : super(_value, _then);

  /// Create a copy of BackupState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
@JsonSerializable()
class _$BackupStateUnreadyImpl implements BackupStateUnready {
  const _$BackupStateUnreadyImpl({final String? $type})
      : $type = $type ?? 'unready';

  factory _$BackupStateUnreadyImpl.fromJson(Map<String, dynamic> json) =>
      _$$BackupStateUnreadyImplFromJson(json);

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'BackupState.unready()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$BackupStateUnreadyImpl);
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime? downloadDate) noBackup,
    required TResult Function(DateTime lastBackupDate) uploaded,
    required TResult Function(DateTime startDate, int progress) uploading,
    required TResult Function(DateTime lastDate) downloaded,
    required TResult Function(int progress) downloading,
    required TResult Function(DateTime lastDate) restored,
    required TResult Function() unready,
  }) {
    return unready();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime? downloadDate)? noBackup,
    TResult? Function(DateTime lastBackupDate)? uploaded,
    TResult? Function(DateTime startDate, int progress)? uploading,
    TResult? Function(DateTime lastDate)? downloaded,
    TResult? Function(int progress)? downloading,
    TResult? Function(DateTime lastDate)? restored,
    TResult? Function()? unready,
  }) {
    return unready?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime? downloadDate)? noBackup,
    TResult Function(DateTime lastBackupDate)? uploaded,
    TResult Function(DateTime startDate, int progress)? uploading,
    TResult Function(DateTime lastDate)? downloaded,
    TResult Function(int progress)? downloading,
    TResult Function(DateTime lastDate)? restored,
    TResult Function()? unready,
    required TResult orElse(),
  }) {
    if (unready != null) {
      return unready();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BackupStateNoBackUp value) noBackup,
    required TResult Function(BackupStateUploaded value) uploaded,
    required TResult Function(BackupStateUploading value) uploading,
    required TResult Function(BackupStateDownloaded value) downloaded,
    required TResult Function(BackupStateDownloading value) downloading,
    required TResult Function(BackupStateRestored value) restored,
    required TResult Function(BackupStateUnready value) unready,
  }) {
    return unready(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BackupStateNoBackUp value)? noBackup,
    TResult? Function(BackupStateUploaded value)? uploaded,
    TResult? Function(BackupStateUploading value)? uploading,
    TResult? Function(BackupStateDownloaded value)? downloaded,
    TResult? Function(BackupStateDownloading value)? downloading,
    TResult? Function(BackupStateRestored value)? restored,
    TResult? Function(BackupStateUnready value)? unready,
  }) {
    return unready?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BackupStateNoBackUp value)? noBackup,
    TResult Function(BackupStateUploaded value)? uploaded,
    TResult Function(BackupStateUploading value)? uploading,
    TResult Function(BackupStateDownloaded value)? downloaded,
    TResult Function(BackupStateDownloading value)? downloading,
    TResult Function(BackupStateRestored value)? restored,
    TResult Function(BackupStateUnready value)? unready,
    required TResult orElse(),
  }) {
    if (unready != null) {
      return unready(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$BackupStateUnreadyImplToJson(
      this,
    );
  }
}

abstract class BackupStateUnready implements BackupState {
  const factory BackupStateUnready() = _$BackupStateUnreadyImpl;

  factory BackupStateUnready.fromJson(Map<String, dynamic> json) =
      _$BackupStateUnreadyImpl.fromJson;
}

BackupInfo _$BackupInfoFromJson(Map<String, dynamic> json) {
  return _BackupInfo.fromJson(json);
}

/// @nodoc
mixin _$BackupInfo {
  bool get enableAutoBackup => throw _privateConstructorUsedError;
  BackupState get state => throw _privateConstructorUsedError;

  /// Serializes this BackupInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BackupInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BackupInfoCopyWith<BackupInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BackupInfoCopyWith<$Res> {
  factory $BackupInfoCopyWith(
          BackupInfo value, $Res Function(BackupInfo) then) =
      _$BackupInfoCopyWithImpl<$Res, BackupInfo>;
  @useResult
  $Res call({bool enableAutoBackup, BackupState state});

  $BackupStateCopyWith<$Res> get state;
}

/// @nodoc
class _$BackupInfoCopyWithImpl<$Res, $Val extends BackupInfo>
    implements $BackupInfoCopyWith<$Res> {
  _$BackupInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BackupInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enableAutoBackup = null,
    Object? state = null,
  }) {
    return _then(_value.copyWith(
      enableAutoBackup: null == enableAutoBackup
          ? _value.enableAutoBackup
          : enableAutoBackup // ignore: cast_nullable_to_non_nullable
              as bool,
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as BackupState,
    ) as $Val);
  }

  /// Create a copy of BackupInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BackupStateCopyWith<$Res> get state {
    return $BackupStateCopyWith<$Res>(_value.state, (value) {
      return _then(_value.copyWith(state: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BackupInfoImplCopyWith<$Res>
    implements $BackupInfoCopyWith<$Res> {
  factory _$$BackupInfoImplCopyWith(
          _$BackupInfoImpl value, $Res Function(_$BackupInfoImpl) then) =
      __$$BackupInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool enableAutoBackup, BackupState state});

  @override
  $BackupStateCopyWith<$Res> get state;
}

/// @nodoc
class __$$BackupInfoImplCopyWithImpl<$Res>
    extends _$BackupInfoCopyWithImpl<$Res, _$BackupInfoImpl>
    implements _$$BackupInfoImplCopyWith<$Res> {
  __$$BackupInfoImplCopyWithImpl(
      _$BackupInfoImpl _value, $Res Function(_$BackupInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of BackupInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enableAutoBackup = null,
    Object? state = null,
  }) {
    return _then(_$BackupInfoImpl(
      enableAutoBackup: null == enableAutoBackup
          ? _value.enableAutoBackup
          : enableAutoBackup // ignore: cast_nullable_to_non_nullable
              as bool,
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as BackupState,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BackupInfoImpl implements _BackupInfo {
  const _$BackupInfoImpl(
      {this.enableAutoBackup = false,
      this.state = const BackupState.noBackup(null)});

  factory _$BackupInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$BackupInfoImplFromJson(json);

  @override
  @JsonKey()
  final bool enableAutoBackup;
  @override
  @JsonKey()
  final BackupState state;

  @override
  String toString() {
    return 'BackupInfo(enableAutoBackup: $enableAutoBackup, state: $state)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BackupInfoImpl &&
            (identical(other.enableAutoBackup, enableAutoBackup) ||
                other.enableAutoBackup == enableAutoBackup) &&
            (identical(other.state, state) || other.state == state));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, enableAutoBackup, state);

  /// Create a copy of BackupInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BackupInfoImplCopyWith<_$BackupInfoImpl> get copyWith =>
      __$$BackupInfoImplCopyWithImpl<_$BackupInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BackupInfoImplToJson(
      this,
    );
  }
}

abstract class _BackupInfo implements BackupInfo {
  const factory _BackupInfo(
      {final bool enableAutoBackup,
      final BackupState state}) = _$BackupInfoImpl;

  factory _BackupInfo.fromJson(Map<String, dynamic> json) =
      _$BackupInfoImpl.fromJson;

  @override
  bool get enableAutoBackup;
  @override
  BackupState get state;

  /// Create a copy of BackupInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BackupInfoImplCopyWith<_$BackupInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
