import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'dart:io';

import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/controller/nurture_types_controller.dart';
import 'package:flower_timemachine/models/notification_model.dart';
import 'package:flutter/services.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:icloud_storage/icloud_storage.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

part 'backup_controller.freezed.dart';
part 'backup_controller.g.dart';

@freezed
sealed class BackupState with _$BackupState {
  const factory BackupState.noBackup(DateTime? downloadDate) = BackupStateNoBackUp;
  const factory BackupState.uploaded(DateTime lastBackupDate) = BackupStateUploaded;
  const factory BackupState.uploading(DateTime startDate, int progress) = BackupStateUploading;
  const factory BackupState.downloaded(DateTime lastDate) = BackupStateDownloaded;
  const factory BackupState.downloading(int progress) = BackupStateDownloading;
  const factory BackupState.restored(DateTime lastDate) = BackupStateRestored;
  const factory BackupState.unready() = BackupStateUnready;

  factory BackupState.fromJson(Map<String, dynamic> json) => _$BackupStateFromJson(json);
}

@freezed
sealed class BackupInfo with _$BackupInfo {
  const factory BackupInfo({
    @Default(false) bool enableAutoBackup,
    @Default(BackupState.noBackup(null)) BackupState state,
  }) = _BackupInfo;

  factory BackupInfo.fromJson(Map<String, dynamic> json) => _$BackupInfoFromJson(json);
}

@riverpod
class BackupController extends _$BackupController {
  static const containerId = "iCloud.com.chenwuapp.flower";
  List<StreamSubscription<double>>? lastUploadSubscriptions;
  Timer? _timer;

  bool autoBackupModel = false;

  @override
  BackupInfo build() {
    ref.onDispose(onDispose);

    final stateData = ShareConfig.getBackupState();
    if (stateData != null) {
      final stateInfo = BackupInfo.fromJson(json.decode(stateData));

      if (stateInfo.state is BackupStateUploading) {
        final startDate = stateInfo.state.whenOrNull(uploading: (startDate, _) => startDate);
        _startRefreshUploadProcess(startDate ?? DateTime.now(), null);
      } else if (stateInfo.state is BackupStateDownloading) {
        _startRefreshDownloadProcess();
      }
      return stateInfo;
    } else {
      return const BackupInfo(state: BackupState.noBackup(null));
    }
  }

  Future<void> upload() async {
    final startDate = DateTime.now();
    setState(BackupState.uploading(startDate, 0));

    final icloudFiles = HashSet<String>();

    await _deleteICloudFiles();

    try {
      icloudFiles.addAll(await uploadFolder(Global.photoRecordDir, "photo_records"));
      icloudFiles.addAll(await uploadFolder(Global.avatarDir, "avatar"));
      icloudFiles.add(await uploadFile(await DB.getDatabaseFilePath(), null));

      _startRefreshUploadProcess(startDate, icloudFiles);
    } on PlatformException catch (e, s) {
      print(e.details);
      setState(const BackupState.unready());
      await Sentry.captureException(e, stackTrace: s);
    }
  }

  Future<void> download() async {
    setState(const BackupState.downloading(0));

    Hint? errorHint;
    try {
      final baseDir = await backupDir;
      final cloudFiles = await ICloudStorage.gather(containerId: containerId);

      if (cloudFiles.isEmpty) {
        setState(BackupState.noBackup(DateTime.now()));
        return;
      }

      errorHint = Hint.withAttachment(formatErrorHint(cloudFiles));

      for (final f in cloudFiles) {
        // 包含空格的文件名是冲突产生的，忽略
        if (f.relativePath.contains("%20")) {
          continue;
        }
        final localPath = "$baseDir/${f.relativePath}";

        final localPathDir = File(localPath).parent;
        if (!await localPathDir.exists()) {
          await localPathDir.create(recursive: true);
        }

        await ICloudStorage.download(
          containerId: containerId,
          relativePath: f.relativePath,
          destinationFilePath: localPath,
        );
      }
      _startRefreshDownloadProcess();
    } on PlatformException catch (e, s) {
      setState(const BackupState.unready());
      await Sentry.captureException(e, stackTrace: s, hint: errorHint);
    }
  }

  Future<void> restore() async {
    await DB.close();

    await _deleteLocalFiles("${Global.photoRecordDir}/photo_records");
    await _deleteLocalFiles("${Global.photoRecordDir}/avatar");

    final baseDir = await Global.getDataDir();

    final backupDirPath = "${await backupDir}/";
    final dir = Directory(backupDirPath);
    final fileList = dir.list(recursive: true);
    await for (final f in fileList) {
      final stat = await f.stat();
      if (stat.type == FileSystemEntityType.directory) {
        continue;
      }

      final relativePath = f.path.substring(backupDirPath.length);

      final dstFile = File("$baseDir/$relativePath");
      if (await dstFile.exists()) {
        await dstFile.delete(recursive: true);
      }
      if (!await dstFile.parent.exists()) {
        await dstFile.parent.create(recursive: true);
      }

      await f.rename(dstFile.path);
    }

    if (await dir.exists()) {
      await dir.delete(recursive: true);
    }

    await NotificationModel.clearOldRecord();
    await NurtureTypesController.get().init();

    setState(BackupState.restored(DateTime.now()));
  }

  Future<bool> readyRestore() async {
    final backupDirPath = "${await backupDir}/";
    final dir = Directory(backupDirPath);

    return await dir.exists();
  }

  Future<HashSet<String>> uploadFolder(String folderPath, String? cloudDir) async {
    final ret = HashSet<String>();
    var fileList = Directory(folderPath).list();
    await for (FileSystemEntity file in fileList) {
      final cloudFile = await uploadFile(file.path, cloudDir);
      ret.add(cloudFile);
    }

    return ret;
  }

  Future<String> uploadFile(String filePath, String? cloudDir) async {
    String cloudFilePath = File(filePath).uri.pathSegments.last;

    if (cloudDir != null) {
      cloudFilePath = "$cloudDir/$cloudFilePath";
    }

    await ICloudStorage.upload(containerId: containerId, filePath: filePath, destinationRelativePath: cloudFilePath);

    return cloudFilePath;
  }

  void _startRefreshUploadProcess(DateTime startDate, HashSet<String>? icloudFiles) {
    if (_timer != null) {
      _timer?.cancel();
    }

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      final newState = await checkUploadState(startDate, icloudFiles);
      if (newState is BackupStateUploaded) {
        _timer?.cancel();
        _timer = null;
      }

      setState(newState);
    });
  }

  void _startRefreshDownloadProcess() {
    _timer?.cancel();

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      final newState = await checkDownloadState();
      if (newState is! BackupStateDownloading) {
        _timer?.cancel();
        _timer = null;
      }

      setState(newState);
    });
  }

  void autoBackupIfNecessary() async {
    autoBackupModel = true;
    if (!state.enableAutoBackup) {
      return;
    }

    if (state.state is BackupStateUnready) {
      return;
    } else if (state.state is BackupStateUploading) {
      final lastBackupDate = state.state.whenOrNull(uploading: (startDate, _) => startDate);
      setState(await checkUploadState(lastBackupDate ?? DateTime.now(), null));
    } else if (state.state is BackupStateDownloading) {
      setState(await checkDownloadState());
    }

    final lastBackupDate = state.state.whenOrNull(
      noBackup: (d) => null,
      uploaded: (d) => d,
      uploading: (d, _) => d,
      downloaded: (d) => null,
      downloading: (d) => null,
      restored: (d) => null,
    );

    // final lastBackupDate = null;
    // print("auto back");

    if (lastBackupDate == null || DateTime.now().difference(lastBackupDate).inDays > 1) {
      upload();
    }
  }

  void setAutoBackupState(bool enable) {
    state = state.copyWith(enableAutoBackup: enable);

    final stateData = json.encode(state.toJson());
    ShareConfig.setBackupState(stateData);
  }

  void setState(BackupState newState) {
    final BackupInfo newInfo = state.copyWith(state: newState);

    final stateData = json.encode(newInfo.toJson());
    ShareConfig.setBackupState(stateData);

    if (!autoBackupModel) {
      state = newInfo;
    }
  }

  Future<BackupState> checkDownloadState() async {
    try {
      final fileList = await ICloudStorage.gather(containerId: containerId);

      if (fileList.isEmpty) {
        return BackupState.noBackup(DateTime.now());
      }

      int downloaded = 0;
      for (final f in fileList) {
        if (f.downloadStatus == DownloadStatus.current) {
          downloaded += 1;
        }
      }

      final process = ((downloaded / fileList.length) * 100).round();

      if (process >= 100) {
        return BackupState.downloaded(DateTime.now());
      } else {
        return BackupState.downloading(process);
      }
    } on PlatformException catch (e, s) {
      await Sentry.captureException(e, stackTrace: s);
      return const BackupState.unready();
    }
  }

  Future<BackupState> checkUploadState(DateTime startDate, HashSet<String>? icloudFiles) async {
    try {
      final fileList = await ICloudStorage.gather(containerId: containerId);

      if (fileList.isEmpty) {
        return BackupState.uploaded(DateTime.now());
      }

      int uploaded = 0;
      int fileListLen = 0;
      for (final f in fileList) {
        print("${f.relativePath} ${f.isUploading}, ${f.contentChangeDate}, ${f.hasUnresolvedConflicts}");
        // FIXME: icloud 为了避免冲突，会在上传的文件加多一个 uuid 后缀，所以根据 icloudFiles 判断，但是这个实例初始化时拿不到 icloudFiles
        if (icloudFiles?.contains(f.relativePath) ?? true) {
          fileListLen += 1;
          if (f.isUploaded) {
            uploaded += 1;
          }
        }
      }

      if (fileListLen <= 0) {
        return BackupState.uploading(startDate, 0);
      }

      final process = ((uploaded / fileListLen) * 100).round();

      if (process >= 100) {
        return BackupState.uploaded(DateTime.now());
      } else {
        return BackupState.uploading(startDate, process);
      }
    } on PlatformException catch (e, s) {
      await Sentry.captureException(e, stackTrace: s);
      return const BackupState.unready();
    }
  }

  static SentryAttachment formatErrorHint(List<ICloudFile> files) {
    String msg = "";
    for (final f in files) {
      msg +=
          "${f.relativePath}: ${f.creationDate}, ${f.contentChangeDate}, ${f.sizeInBytes}, ${f.downloadStatus}, ${f.hasUnresolvedConflicts}\n";
    }

    return SentryAttachment.fromIntList(utf8.encode(msg), "error.txt");
  }

  void onDispose() {
    _timer?.cancel();
    _timer = null;
  }

  Future<void> _deleteICloudFiles() async {
    final files = await ICloudStorage.gather(containerId: containerId);
    for (final f in files) {
      try {
        if (f.isUploading || f.isDownloading) {
          continue;
        }

        await ICloudStorage.delete(containerId: containerId, relativePath: Uri.decodeComponent(f.relativePath));
      } on PlatformException catch (e) {
        print("${f.relativePath}: $e");
      }
    }
  }

  Future<void> _deleteLocalFiles(String dir) async {
    final dirObj = Directory(dir);
    try {
      if (await dirObj.exists()) {
        await dirObj.delete(recursive: true);
      }
    } catch (e) {
      print(e);
    }
  }

  Future<String> get backupDir async => "${await Global.getDataDir()}/backup";
}
