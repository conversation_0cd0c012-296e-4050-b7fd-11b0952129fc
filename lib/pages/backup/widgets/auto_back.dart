
import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/controller/user_controller.dart';
import 'package:flower_timemachine/pages/backup/controller/backup_controller.dart';
import 'package:flower_timemachine/pages/vip_tips/vip_tips.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'setting_item.dart';

class BackupAutoBack extends ConsumerWidget {
  const BackupAutoBack({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final enableAutoBackup = ref.watch(backupControllerProvider.select((value) => value.enableAutoBackup));
    return BackupSettingItem(
          text: 'backup.auto_backup'.tr(),
          widget: SizedBox(
            height: 20,
            child: Switch(
                value: enableAutoBackup,
                onChanged: (v) {
                  if (!UserController.get().isVip()) {
                    VipTipsDialog.show("free_user_limit".tr(), context);
                    return;
                  }

                  ref.read(backupControllerProvider.notifier).setAutoBackupState(v);
                }
            ),
          )
    );
  }
}