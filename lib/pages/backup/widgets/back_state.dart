
import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/pages/backup/controller/backup_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import 'setting_item.dart';

final dateFormat = DateFormat.yMMMMd();
final timeFormat = DateFormat.Hm();

class BackupBackState extends ConsumerWidget {
  const BackupBackState({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(backupControllerProvider.select((value) => value.state));
    return state.when(
      noBackup: (date) => BackupSettingItem(
        text: 'backup.backup_state'.tr(),
        widget: Text(
            date == null ? "backup.no_backup".tr() : "backup.icloud_no_data".tr(),
          style: const TextStyle(color: Colors.black45)
        )
      ),
      unready: () => BackupSettingItem(
          text: 'backup.backup_state'.tr(),
          widget: const Text(
            "backup.no_signed",
            style: TextStyle(color: Colors.red),
          ).tr()
      ),
      uploaded: (date) => BackupSettingItem(
          text: 'backup.latest_upload'.tr(),
          widget: Text(
            "${dateFormat.format(date)} ${timeFormat.format(date)}",
            style: const TextStyle(color: Color(0xFFFFB000)),
          )
      ),
      uploading: (startDate, progress) => BackupSettingItem(
          text: 'backup.backing'.tr(),
          widget: Text(
            "$progress%",
            style: const TextStyle(color: Color(0xFFFFB000)),
          )
      ),
      downloaded: (date) => BackupSettingItem(
          text: 'backup.latest_download'.tr(),
          widget: Text(
            "${dateFormat.format(date)} ${timeFormat.format(date)}",
            style: const TextStyle(color: Color(0xFFFFB000)),
          )
      ),
      downloading: (progress) => BackupSettingItem(
          text: 'backup.downloading'.tr(),
          widget: Text(
            "$progress%",
            style: const TextStyle(color: Color(0xFFFFB000)),
          )
      ),
      restored: (date) => BackupSettingItem(
          text: 'backup.last_restore'.tr(),
          widget: Text(
            "${dateFormat.format(date)} ${timeFormat.format(date)}",
            style: const TextStyle(color: Color(0xFFFFB000)),
          )
      )
    );
  }
}