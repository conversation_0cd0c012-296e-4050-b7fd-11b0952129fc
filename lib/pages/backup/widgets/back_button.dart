
import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/dialog_utils.dart';
import 'package:flower_timemachine/controller/user_controller.dart';
import 'package:flower_timemachine/pages/backup/controller/backup_controller.dart';
import 'package:flower_timemachine/pages/vip_tips/vip_tips.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:icloud_availability/icloud_availability.dart';

import 'setting_item.dart';

class BackupBackButton extends ConsumerWidget {
  const BackupBackButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(backupControllerProvider.select((value) => value.state));
    Color? textColor;
    Color? iconColor;
    VoidCallback? onTap;
    if (state is BackupStateDownloading) {
      textColor = Colors.black38;
      iconColor = Colors.black38;
    } else {
      onTap = () => _onTap(context, ref);
    }

    return BackupSettingItem(
      text: "backup.upload_now".tr(),
      textColor: textColor,
      widget: Icon(Icons.arrow_forward_ios_outlined, color: iconColor),
      onTap: onTap
    );
  }

  void _onTap(BuildContext context, WidgetRef ref) async {
    if (!UserController.get().isVip()) {
      VipTipsDialog.show("free_user_limit".tr(), context);
      return;
    }

    if (!(await IcloudAvailability.checkAvailability() ?? true)) {
      if (context.mounted) {
        showMessageDialog(
            "prompt".tr(),
            "backup.icloud_unavailable".tr(),
            context
        );
      }

      return;
    }


    ref.read(backupControllerProvider.notifier).upload();
  }
}