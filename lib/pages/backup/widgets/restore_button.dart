
import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/dialog_utils.dart';
import 'package:flower_timemachine/controller/user_controller.dart';
import 'package:flower_timemachine/pages/backup/controller/backup_controller.dart';
import 'package:flower_timemachine/pages/vip_tips/vip_tips.dart';
import 'package:flower_timemachine/widgets/loading_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'setting_item.dart';

class BackupRestoredButton extends ConsumerStatefulWidget {
  const BackupRestoredButton({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _BackupRestoredButtonState();
}

class _BackupRestoredButtonState extends ConsumerState<BackupRestoredButton> {
  @override
  Widget build(BuildContext context) {
    final state = ref.watch(backupControllerProvider.select((value) => value.state));
    Color? textColor;
    Color? iconColor;
    VoidCallback? onTapFunc;
    if (state is BackupStateDownloaded) {
      onTapFunc = onTap;
    } else {
      textColor = Colors.black38;
      iconColor = Colors.black38;
    }

    return BackupSettingItem(
        text: "backup.restore_backup".tr(),
        textColor: textColor,
        widget: Icon(Icons.arrow_forward_ios_outlined, color: iconColor),
        onTap: onTapFunc
    );
  }

  void onTap() async {
    if (!UserController.get().isVip()) {
      VipTipsDialog.show("free_user_limit".tr(), context);
      return;
    }

    final controller = ref.read(backupControllerProvider.notifier);
    if (!await controller.readyRestore()) {
      if (mounted) {
        showMessageDialog("prompt".tr(), "backup.backup_data_not_reader".tr(), context);
      }
      return;
    }

    if (mounted) {
      final isOk = await showAlertDialog("alert".tr(), "backup.restore_alert".tr(), context);
      if (!(isOk ?? false)) {
        return;
      }
    }

    LoadingDialog.show(text: "backup.restoring".tr());
    ref.read(backupControllerProvider.notifier).restore();
    LoadingDialog.dismiss();

    if (mounted) {
      showMessageDialog("prompt".tr(), "backup.restore_success".tr(), context);
    }
  }
}