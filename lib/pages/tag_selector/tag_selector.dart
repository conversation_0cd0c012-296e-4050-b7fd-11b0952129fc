import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/dialog_utils.dart';
import 'package:flower_timemachine/controller/user_controller.dart';
import 'package:flower_timemachine/pages/vip_tips/vip_tips.dart';
import 'package:flower_timemachine/widgets/error_reporter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../../controller/tag_list_controller.dart';
import '../../models/tag_info.dart';
import '../../widgets/tag_info_container.dart';
import 'widgets/selectable_tag_info_container.dart';

class TagSelectorPage extends ConsumerWidget {
  const TagSelectorPage({super.key, required this.selectedTags});

  static const routeName = 'add_flower_tag_page';

  final List<TagInfo> selectedTags;

  @override
  Widget build(BuildContext context, WidgetRef ref) => Scaffold(
        appBar: AppBar(
          elevation: 0,
        ),
        body: SafeArea(
          child: ref.watch(tagListControllerProvider).when(
                error: (error, stackTrace) => ErrorReporter(error: error, stackTrace: stackTrace),
                loading: () => Container(
                  height: 40,
                  alignment: Alignment.center,
                  child: const SizedBox(height: 100, width: 100, child: CircularProgressIndicator()),
                ),
                data: (data) => Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Center(
                        child: Text('tag_manager_page.select_tag'.tr(),
                            style: const TextStyle(fontSize: 25, color: Colors.black)),
                      ),
                      const SizedBox(height: 30),
                      Expanded(
                        child: SingleChildScrollView(
                          scrollDirection: Axis.vertical,
                          child: Wrap(spacing: 10, runSpacing: 10, children: _buildItems(data, context, ref)),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
        ),
      );

  List<Widget> _buildItems(List<TagInfo> tags, BuildContext context, WidgetRef ref) {
    List<Widget> result = [];
    for (final tag in tags) {
      final isSelected = selectedTags.contains(tag);
      result.add(SelectableTagInfoContainer(
          tagName: tag.name,
          isSelected: isSelected,
          onChange: (isSelected) {
            if (isSelected) {
              selectedTags.add(tag);
            } else {
              selectedTags.remove(tag);
            }

            return isSelected;
          }));
    }

    result.add(GestureDetector(
        onTap: () => onTapAddTag(context, ref),
        child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: const Color(0xfff0f0f0),
            ),
            child: TagInfoContainer(tagName: 'tag_manager_page.add_tag'.tr()))));

    return result;
  }

  void onTapAddTag(BuildContext context, WidgetRef ref) async {
    final controller = ref.read(tagListControllerProvider.notifier);
    final isVip = UserController.get().isVip();
    if (!isVip && controller.getNoVipLimit()) {
      VipTipsDialog.show("tag_manager_page.free_user_limit".tr(), context);
      return;
    }

    final newTag = await showTextDialog('tag_manager_page.enter_tag_name'.tr(), 16, context);
    if (newTag == null || newTag.isEmpty) {
      return;
    }

    if (await controller.checkSame(newTag)) {
      Fluttertoast.showToast(msg: "tag_manager_page.same_tag".tr(), toastLength: Toast.LENGTH_SHORT);
      return;
    }

    final tagInfo = await controller.add(newTag);
    selectedTags.add(tagInfo);
  }
}
