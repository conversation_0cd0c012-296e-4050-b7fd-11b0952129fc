
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../../widgets/tag_info_container.dart';

class SelectableTagInfoContainer extends HookWidget {
  const SelectableTagInfoContainer({
    super.key,
    required this.isSelected,
    required this.tagName,
    required this.onChange
  });

  final bool isSelected;
  final String tagName;
  final bool Function(bool) onChange;

  @override
  Widget build(BuildContext context) {
    final selectedState = useState(isSelected);
    return GestureDetector(
      onTap: () {
        selectedState.value = onChange(!selectedState.value);
      },
      child: selectedState.value
        ? Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Theme.of(context).primaryColor,
          ),
          child: DefaultTextStyle(
            style: const TextStyle(color: Colors.white),
            child: TagInfoContainer(tagName: tagName)
          )
      ) : TagInfoContainer(tagName: tagName)
    );
  }

}