import 'package:flower_timemachine/models/photo_record.dart';
import 'package:flower_timemachine/widgets/error_reporter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flower_timemachine/pages/album/controller/album_controller.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/pages/album/widgets/album_grid_view.dart';
import 'package:flower_timemachine/pages/album/widgets/album_timeline_view.dart';

class AlbumPage extends ConsumerWidget {
  const AlbumPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final photos = ref.watch(albumControllerProvider);

    return photos.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (err, stack) => ErrorReporter(error: err, stackTrace: stack),
      data: (items) => _buildContent(context, items),
    );
  }

  Widget _buildContent(BuildContext context, List<PhotoRecord> items) {
    if (items.isEmpty) {
      return _buildEmpty();
    }

    final showTimeline = ShareConfig.getShowAlbumTimeline();
    if (!showTimeline) {
      return AlbumGridView(photos: items);
    }

    return const AlbumTimelineView();
  }

  Widget _buildEmpty() {
    return Center(child: Text('album_empty'.tr()));
  }
}
