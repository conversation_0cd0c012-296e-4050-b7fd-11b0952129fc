import 'package:flower_timemachine/models/photo_record.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flower_timemachine/pages/album/controller/album_controller.dart';

part 'album_group_controller.g.dart';

class AlbumGroup {
  final DateTime date;
  final List<PhotoRecord> photos;

  AlbumGroup({required this.date, required this.photos});
}

@riverpod
class AlbumGroupController extends _$AlbumGroupController {
  @override
  List<AlbumGroup> build() {
    final photos = ref.watch(albumControllerProvider);
    return photos.when(
      data: (items) => _groupPhotos(items),
      loading: () => [],
      error: (_, __) => [],
    );
  }

  List<AlbumGroup> _groupPhotos(List<PhotoRecord> photos) {
    if (photos.isEmpty) {
      return [];
    }

    final groupedPhotos = <DateTime, List<PhotoRecord>>{};

    for (var photo in photos) {
      final date = DateTime.fromMillisecondsSinceEpoch(photo.time * 1000);
      // 只保留年月日
      final dateKey = DateTime(date.year, date.month, date.day);
      groupedPhotos.putIfAbsent(dateKey, () => []).add(photo);
    }

    // Sort dates in descending order
    final sortedDates = groupedPhotos.keys.toList()..sort((a, b) => b.compareTo(a));

    return sortedDates.map((date) => AlbumGroup(date: date, photos: groupedPhotos[date]!)).toList();
  }
}
