import 'package:flower_timemachine/models/photo_record.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flower_timemachine/controller/album_selector_controller.dart';

part 'album_controller.g.dart';

@riverpod
class AlbumController extends _$AlbumController {
  static const int limit = 100;

  bool _isLoading = false;
  bool _hasMore = true;

  @override
  Future<List<PhotoRecord>> build() async {
    ref.watch(albumCurrentFilterProvider);
    return _loadPhotos(0);
  }

  int checkCurrentPhotoIndex(PhotoRecord photo) {
    return state.value?.indexOf(photo) ?? 0;
  }

  Future<List<PhotoRecord>> _loadPhotos(int offset) async {
    final filters = ref.read(albumCurrentFilterProvider);

    if (filters.isEmpty) {
      final photos = await PhotoRecord.get(offset: offset, limit: limit, timeOrder: 'DESC');
      _hasMore = photos.length == limit;
      return photos;
    }

    final flowerIds = filters.where((f) => f.type == AlbumFilteDataType.flower).map((f) => f.id).toList();
    final tagIds = filters.where((f) => f.type == AlbumFilteDataType.tag).map((f) => f.id).toList();

    final photos = await PhotoRecord.getByFilters(
      offset: offset,
      limit: limit,
      timeOrder: 'DESC',
      flowerIds: flowerIds,
      tagIds: tagIds,
    );

    _hasMore = photos.length == limit;
    return photos;
  }

  Future<void> loadMore() async {
    if (_isLoading || !_hasMore) {
      return;
    }

    _isLoading = true;
    try {
      final currentPhotos = state.value ?? [];
      final newPhotos = await _loadPhotos(currentPhotos.length);

      if (newPhotos.isEmpty) {
        return;
      }

      state = AsyncValue.data([...currentPhotos, ...newPhotos]);
    } finally {
      _isLoading = false;
    }
  }

  bool get hasMore => _hasMore;

  int get length => state.value?.length ?? 0;

  PhotoRecord? getPhoto(int index) => state.value?[index];
}
