import 'package:flower_timemachine/pages/album/controller/album_group_controller.dart';
import 'package:flower_timemachine/pages/album/widgets/album_grid_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';

class AlbumTimelineView extends ConsumerWidget {
  const AlbumTimelineView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final groups = ref.watch(albumGroupControllerProvider);

    if (groups.isEmpty) {
      return const SizedBox.shrink();
    }

    final dateFormat = DateFormat.yMMMd(context.locale.languageCode);

    return ListView.builder(
      itemCount: groups.length,
      itemBuilder: (context, index) {
        final group = groups[index];
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                dateFormat.format(group.date),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            AlbumGridView(photos: group.photos),
          ],
        );
      },
    );
  }
}
