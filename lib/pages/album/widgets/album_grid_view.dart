import 'package:flower_timemachine/controller/album_export_controller.dart';
import 'package:flower_timemachine/controller/garden_controller.dart';
import 'package:flower_timemachine/models/photo_record.dart';
import 'package:flower_timemachine/pages/album/controller/album_controller.dart';
import 'package:flower_timemachine/pages/album/show_photo.dart';
import 'package:flower_timemachine/widgets/media_preview/media_preview.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class AlbumGridView extends ConsumerWidget {
  final List<PhotoRecord> photos;

  const AlbumGridView({
    super.key,
    required this.photos,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appNavigationState = ref.watch(appNavigationStateProvider);
    final selectedPhotos = ref.watch(albumExportControllerProvider);

    return GridView.builder(
      shrinkWrap: true,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 2,
        mainAxisSpacing: 2,
      ),
      itemCount: photos.length,
      itemBuilder: (context, index) => _buildItem(
        context,
        index,
        ref,
        appNavigationState == AppNavigationState.exportAlbum,
        selectedPhotos,
      ),
    );
  }

  Widget _buildItem(
    BuildContext context,
    int index,
    WidgetRef ref,
    bool isExport,
    List<PhotoRecord> selectedPhotos,
  ) {
    final controller = ref.read(albumControllerProvider.notifier);
    if (index >= photos.length - 1 && controller.hasMore) {
      controller.loadMore();
    }

    final photo = photos[index];
    final photoWidget = MediaPreview(
      path: photo.displayPath,
      mediaType: photo.mediaType,
      width: 256,
    );

    if (isExport) {
      final isSelected = selectedPhotos.contains(photo);
      return Stack(
        children: [
          Positioned.fill(child: photoWidget),
          Positioned(
            top: 4,
            right: 4,
            child: SizedBox(
              width: 36,
              child: Transform.scale(
                scale: 1.3,
                child: Checkbox(
                  shape: const CircleBorder(),
                  value: isSelected,
                  onChanged: (value) => _onTapExport(ref, photo, value ?? false),
                ),
              ),
            ),
          ),
        ],
      );
    } else {
      return GestureDetector(
        onTap: () => _onTapAlbum(context, photo),
        child: photoWidget,
      );
    }
  }

  void _onTapExport(WidgetRef ref, PhotoRecord photo, bool isSelected) {
    if (isSelected) {
      ref.read(albumExportControllerProvider.notifier).add(photo);
    } else {
      ref.read(albumExportControllerProvider.notifier).remove(photo);
    }
  }

  void _onTapAlbum(BuildContext context, PhotoRecord photo) {
    Navigator.pushNamed(
      context,
      AlbumShowPhotoPage.routeName,
      arguments: photo,
    );
  }
}
