import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/controller/garden_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flower_timemachine/pages/home/<USER>/album_filter_menu.dart';

class AlbumAppBar extends ConsumerWidget implements PreferredSizeWidget {
  const AlbumAppBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.white.withOpacity(0.9),
      title: Text('album'.tr()),
      centerTitle: false,
      actions: [
        const AlbumFilterButton(),
        IconButton(
          icon: const Icon(Icons.download_outlined, size: 25),
          onPressed: () => _onTapShare(ref),
        ),
        const SizedBox(width: 8),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  void _onTapShare(WidgetRef ref) {
    final oldState = ref.read(appNavigationStateProvider);
    ref.read(appNavigationStateProvider.notifier).state =
        oldState == AppNavigationState.home ? AppNavigationState.exportAlbum : AppNavigationState.home;
    // ref.read(albumSelectModeProvider.notifier).state = true;
  }
}
