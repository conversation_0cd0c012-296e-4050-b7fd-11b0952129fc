import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/controller/flower_list_controller.dart';
import 'package:flower_timemachine/controller/garden_controller.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/pages/home/<USER>/home_filter_button.dart';
import 'package:flower_timemachine/pages/search/search.dart';
import 'package:flower_timemachine/types/flower_list_controller_event.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';

class GardenAppBar extends ConsumerWidget implements PreferredSizeWidget {
  const GardenAppBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppBar(
      title: Text(ShareConfig.getGardenTitle()),
      centerTitle: false,
      elevation: 0,
      actions: [
        IconButton(
          icon: SvgPicture.asset("icons/add.svg", height: 24, width: 24),
          iconSize: 30,
          onPressed: () => _onClickAddFlower(context),
          highlightColor: Colors.transparent,
        ),
        IconButton(
          icon: const Icon(Icons.search),
          iconSize: 30,
          onPressed: () => _onClickSearch(context),
          highlightColor: Colors.transparent,
        ),
        IconButton(
          icon: const Icon(Icons.auto_fix_high, size: 25),
          onPressed: () => _onClickAutoNurture(ref),
          highlightColor: Colors.transparent,
        ),
        const SizedBox(width: 8),
        const HomeFilterButton(),
        const SizedBox(width: 15)
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  void _onClickSearch(BuildContext context) async {
    await Navigator.pushNamed(context, Search.routeName);
  }

  void _onClickAutoNurture(WidgetRef ref) {
    ref.read(appNavigationStateProvider.notifier).state =
        ref.read(appNavigationStateProvider) == AppNavigationState.autoNurture
            ? AppNavigationState.home
            : AppNavigationState.autoNurture;
  }

  void _onClickAddFlower(BuildContext context) async {
    Flower? newFlower = (await Navigator.pushNamed(context, 'add_flower')) as Flower?;
    if (newFlower != null) {
      flowerListControllerEventProvider.value = FlowerListEventType.addFlower(newFlower);
    }
  }
}
