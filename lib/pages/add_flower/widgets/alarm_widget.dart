import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/pages/add_flower/controller/add_flower_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_picker_plus/flutter_picker_plus.dart';

class AddFlowerAlarmWidget extends ConsumerStatefulWidget {
  const AddFlowerAlarmWidget({
    super.key,
    required this.type,
    required this.pickerData,
    required this.controller,
  });

  final NurtureType type;
  final List<String> pickerData;
  final AddFlowerController controller;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _AddFlowerAlarmWidgetState();
}

class _AddFlowerAlarmWidgetState extends ConsumerState<AddFlowerAlarmWidget>{
  @override
  Widget build(BuildContext context) {
    if (widget.controller.loadNurtureCycle) {
      final cycle = widget.controller.getNurtureTypeCycle(widget.type);
      Widget cycleText;
      if (cycle <= 0) {
        cycleText = Text("unset".tr(), style: const TextStyle(color: Colors.black54));
      } else {
        cycleText = const Text("add_flower.cycle").tr(
          namedArgs: {"day": widget.pickerData[cycle]}
        );
      }

      var picker = Picker(
          title: Text(widget.type.name),
          adapter: PickerDataAdapter<String>(pickerData: widget.pickerData),
          cancelText: 'cancel'.tr(),
          confirmText: 'confirm'.tr(),
          onConfirm: onConfirm
      );

      return GestureDetector(
        behavior: HitTestBehavior.opaque,
        child: buildBody(cycleText),
        onTap: () => picker.showModal(context),
      );

    } else {
      return buildBody(const CircularProgressIndicator());
    }
  }

  Widget buildBody(Widget cycle) {
    return Container(
        padding: const EdgeInsets.all(5),
        child: Row(children: [
          Container(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: SvgPicture.asset(widget.type.icon, width: 16, height: 16),
          ),
          Text(widget.type.name),
          const Spacer(),
          cycle,
          const SizedBox(width: 5),
          const Icon(Icons.edit)
        ])
    );
  }

  void onConfirm(Picker picker, List<int> selected) {
    setState(() {
      widget.controller.setNurtureTypeCycle(widget.type, selected[0]);
    });
  }
}