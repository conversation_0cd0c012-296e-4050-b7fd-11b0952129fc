import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/models/tag_info.dart';
import 'package:flower_timemachine/pages/tag_selector/tag_selector.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';


class AddFlowerTagSelector extends StatelessWidget {
  const AddFlowerTagSelector({super.key, required this.selectedTags});

  final List<TagInfo> selectedTags;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("add_flower.tag_title".tr(), textAlign: TextAlign.start),
          const SizedBox(height: 5),
          Container(
            padding: const EdgeInsets.all(10),
            constraints: const BoxConstraints(
              minHeight: 50, minWidth: double.infinity
            ),
            decoration: BoxDecoration(
              color: const Color(0xfff7f7f7),
              border: Border.all(width: 1, color: Colors.transparent),
              borderRadius: BorderRadius.circular(5)
            ),
            child: HookBuilder(
              builder: (context) {
                final update = useState(false);
                return GestureDetector(
                    onTap: () async {
                      await Navigator.pushNamed(context, TagSelectorPage.routeName, arguments: selectedTags);
                      update.value = !update.value;
                    },
                    child: Row(
                      children: [
                        Expanded(
                          child: selectedTags.isNotEmpty
                              ? Text(selectedTags.map((e) => e.name).join(', '), style: const TextStyle(fontSize: 18))
                              : Text('add_flower.add_tag'.tr(), style: const TextStyle(fontSize: 18, color: Colors.blue))
                        ),
                        const SizedBox(width: 5),
                        const Icon(Icons.arrow_forward_ios, color: Colors.grey)
                      ],
                    )
                );
              }
            )
          )
        ],
      ),
    );
  }
}