import 'dart:io';
import 'dart:typed_data';

import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/widgets/permission_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:pull_down_button/pull_down_button.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class AvatarChangeableWidget extends StatefulWidget {
  const AvatarChangeableWidget(
      {super.key, required this.avatar, required this.onChange, required this.height, required this.width});

  final Widget avatar;
  final void Function(Uint8List) onChange;
  final double height;
  final double width;

  @override
  State<StatefulWidget> createState() => _AvatarChangeableState();
}

class _AvatarChangeableState extends State<AvatarChangeableWidget> {
  late Widget avatar;
  final ImagePicker picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    avatar = widget.avatar;
  }

  @override
  Widget build(BuildContext context) {
    return PullDownButton(
        itemBuilder: (context) => [
              PullDownMenuItem(
                title: 'from_album'.tr(),
                onTap: byPhotoAlbum,
              ),
              PullDownMenuItem(
                title: 'from_camera'.tr(),
                onTap: byCamera,
              ),
            ],
        buttonBuilder: (context, showMenu) => GestureDetector(
              onTap: showMenu,
              child: SizedBox(
                  width: widget.width,
                  height: widget.height,
                  child: Stack(
                    children: [
                      ClipRRect(borderRadius: BorderRadius.circular(8.0), child: avatar),
                      const Positioned(
                          bottom: 0, right: 0, child: Icon(Icons.camera_alt, size: 15, color: Colors.black45))
                    ],
                  )),
            ));
  }

  void byPhotoAlbum() async {
    final permission = await PhotoManager.requestPermissionExtend();
    if (permission != PermissionState.limited && permission != PermissionState.authorized) {
      if (mounted) {
        await PermissionDialog.show(context, "avatar_changeable_page.add_avatar_permission".tr());
      }
      return;
    }

    final image = await picker.pickImage(source: ImageSource.gallery);
    await pickerCallback(image);
  }

  void byCamera() async {
    try {
      final ret = await picker.pickImage(source: ImageSource.camera);
      await pickerCallback(ret);
    } on PlatformException catch (e) {
      if (e.code == 'camera_access_denied') {
        PermissionDialog.show(context, "avatar_changeable_page.camera_permission".tr());
      }
      return;
    }
  }

  Future<void> pickerCallback(XFile? image) async {
    if (image == null) {
      return;
    }

    if (!mounted) {
      return;
    }

    final imageData = (await Navigator.pushNamed(context, 'edit_image', arguments: File(image.path))) as Uint8List?;
    if (imageData == null) {
      return;
    }

    try {
      await File(image.path).delete();
    } on Exception catch (e, s) {
      Sentry.captureException(e, stackTrace: s);
    }

    widget.onChange(imageData);

    setState(() {
      avatar = Image.memory(imageData);
    });
  }
}
