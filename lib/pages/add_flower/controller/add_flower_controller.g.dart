// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'add_flower_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$getAddFlowerControllerHash() =>
    r'4413435ea783e78496ae68d9129416610611c4ab';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [getAddFlowerController].
@ProviderFor(getAddFlowerController)
const getAddFlowerControllerProvider = GetAddFlowerControllerFamily();

/// See also [getAddFlowerController].
class GetAddFlowerControllerFamily extends Family<AddFlowerController> {
  /// See also [getAddFlowerController].
  const GetAddFlowerControllerFamily();

  /// See also [getAddFlowerController].
  GetAddFlowerControllerProvider call(
    Flower? flower,
  ) {
    return GetAddFlowerControllerProvider(
      flower,
    );
  }

  @override
  GetAddFlowerControllerProvider getProviderOverride(
    covariant GetAddFlowerControllerProvider provider,
  ) {
    return call(
      provider.flower,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'getAddFlowerControllerProvider';
}

/// See also [getAddFlowerController].
class GetAddFlowerControllerProvider
    extends AutoDisposeProvider<AddFlowerController> {
  /// See also [getAddFlowerController].
  GetAddFlowerControllerProvider(
    Flower? flower,
  ) : this._internal(
          (ref) => getAddFlowerController(
            ref as GetAddFlowerControllerRef,
            flower,
          ),
          from: getAddFlowerControllerProvider,
          name: r'getAddFlowerControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$getAddFlowerControllerHash,
          dependencies: GetAddFlowerControllerFamily._dependencies,
          allTransitiveDependencies:
              GetAddFlowerControllerFamily._allTransitiveDependencies,
          flower: flower,
        );

  GetAddFlowerControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.flower,
  }) : super.internal();

  final Flower? flower;

  @override
  Override overrideWith(
    AddFlowerController Function(GetAddFlowerControllerRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: GetAddFlowerControllerProvider._internal(
        (ref) => create(ref as GetAddFlowerControllerRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        flower: flower,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<AddFlowerController> createElement() {
    return _GetAddFlowerControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is GetAddFlowerControllerProvider && other.flower == flower;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, flower.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin GetAddFlowerControllerRef on AutoDisposeProviderRef<AddFlowerController> {
  /// The parameter `flower` of this provider.
  Flower? get flower;
}

class _GetAddFlowerControllerProviderElement
    extends AutoDisposeProviderElement<AddFlowerController>
    with GetAddFlowerControllerRef {
  _GetAddFlowerControllerProviderElement(super.provider);

  @override
  Flower? get flower => (origin as GetAddFlowerControllerProvider).flower;
}

String _$loadFlowerMonthlyCyclesHash() =>
    r'883d005ad4b81af909c1f425f2eb13c08f593468';

/// See also [loadFlowerMonthlyCycles].
@ProviderFor(loadFlowerMonthlyCycles)
const loadFlowerMonthlyCyclesProvider = LoadFlowerMonthlyCyclesFamily();

/// See also [loadFlowerMonthlyCycles].
class LoadFlowerMonthlyCyclesFamily
    extends Family<AsyncValue<FlowerMonthlyCycles>> {
  /// See also [loadFlowerMonthlyCycles].
  const LoadFlowerMonthlyCyclesFamily();

  /// See also [loadFlowerMonthlyCycles].
  LoadFlowerMonthlyCyclesProvider call(
    AddFlowerController controller,
  ) {
    return LoadFlowerMonthlyCyclesProvider(
      controller,
    );
  }

  @override
  LoadFlowerMonthlyCyclesProvider getProviderOverride(
    covariant LoadFlowerMonthlyCyclesProvider provider,
  ) {
    return call(
      provider.controller,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'loadFlowerMonthlyCyclesProvider';
}

/// See also [loadFlowerMonthlyCycles].
class LoadFlowerMonthlyCyclesProvider
    extends AutoDisposeFutureProvider<FlowerMonthlyCycles> {
  /// See also [loadFlowerMonthlyCycles].
  LoadFlowerMonthlyCyclesProvider(
    AddFlowerController controller,
  ) : this._internal(
          (ref) => loadFlowerMonthlyCycles(
            ref as LoadFlowerMonthlyCyclesRef,
            controller,
          ),
          from: loadFlowerMonthlyCyclesProvider,
          name: r'loadFlowerMonthlyCyclesProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$loadFlowerMonthlyCyclesHash,
          dependencies: LoadFlowerMonthlyCyclesFamily._dependencies,
          allTransitiveDependencies:
              LoadFlowerMonthlyCyclesFamily._allTransitiveDependencies,
          controller: controller,
        );

  LoadFlowerMonthlyCyclesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.controller,
  }) : super.internal();

  final AddFlowerController controller;

  @override
  Override overrideWith(
    FutureOr<FlowerMonthlyCycles> Function(LoadFlowerMonthlyCyclesRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: LoadFlowerMonthlyCyclesProvider._internal(
        (ref) => create(ref as LoadFlowerMonthlyCyclesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        controller: controller,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<FlowerMonthlyCycles> createElement() {
    return _LoadFlowerMonthlyCyclesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is LoadFlowerMonthlyCyclesProvider &&
        other.controller == controller;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, controller.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin LoadFlowerMonthlyCyclesRef
    on AutoDisposeFutureProviderRef<FlowerMonthlyCycles> {
  /// The parameter `controller` of this provider.
  AddFlowerController get controller;
}

class _LoadFlowerMonthlyCyclesProviderElement
    extends AutoDisposeFutureProviderElement<FlowerMonthlyCycles>
    with LoadFlowerMonthlyCyclesRef {
  _LoadFlowerMonthlyCyclesProviderElement(super.provider);

  @override
  AddFlowerController get controller =>
      (origin as LoadFlowerMonthlyCyclesProvider).controller;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
