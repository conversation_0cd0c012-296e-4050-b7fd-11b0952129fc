// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'calendar_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$queryMonthMarkedDatesHash() =>
    r'6aeb1e00208d93ddf1e7bf05179c4df8f9eab77c';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [queryMonthMarkedDates].
@ProviderFor(queryMonthMarkedDates)
const queryMonthMarkedDatesProvider = QueryMonthMarkedDatesFamily();

/// See also [queryMonthMarkedDates].
class QueryMonthMarkedDatesFamily
    extends Family<AsyncValue<Map<DateTime, bool>>> {
  /// See also [queryMonthMarkedDates].
  const QueryMonthMarkedDatesFamily();

  /// See also [queryMonthMarkedDates].
  QueryMonthMarkedDatesProvider call(
    DateTime month,
  ) {
    return QueryMonthMarkedDatesProvider(
      month,
    );
  }

  @override
  QueryMonthMarkedDatesProvider getProviderOverride(
    covariant QueryMonthMarkedDatesProvider provider,
  ) {
    return call(
      provider.month,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'queryMonthMarkedDatesProvider';
}

/// See also [queryMonthMarkedDates].
class QueryMonthMarkedDatesProvider
    extends AutoDisposeFutureProvider<Map<DateTime, bool>> {
  /// See also [queryMonthMarkedDates].
  QueryMonthMarkedDatesProvider(
    DateTime month,
  ) : this._internal(
          (ref) => queryMonthMarkedDates(
            ref as QueryMonthMarkedDatesRef,
            month,
          ),
          from: queryMonthMarkedDatesProvider,
          name: r'queryMonthMarkedDatesProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$queryMonthMarkedDatesHash,
          dependencies: QueryMonthMarkedDatesFamily._dependencies,
          allTransitiveDependencies:
              QueryMonthMarkedDatesFamily._allTransitiveDependencies,
          month: month,
        );

  QueryMonthMarkedDatesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.month,
  }) : super.internal();

  final DateTime month;

  @override
  Override overrideWith(
    FutureOr<Map<DateTime, bool>> Function(QueryMonthMarkedDatesRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: QueryMonthMarkedDatesProvider._internal(
        (ref) => create(ref as QueryMonthMarkedDatesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        month: month,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Map<DateTime, bool>> createElement() {
    return _QueryMonthMarkedDatesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is QueryMonthMarkedDatesProvider && other.month == month;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, month.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin QueryMonthMarkedDatesRef
    on AutoDisposeFutureProviderRef<Map<DateTime, bool>> {
  /// The parameter `month` of this provider.
  DateTime get month;
}

class _QueryMonthMarkedDatesProviderElement
    extends AutoDisposeFutureProviderElement<Map<DateTime, bool>>
    with QueryMonthMarkedDatesRef {
  _QueryMonthMarkedDatesProviderElement(super.provider);

  @override
  DateTime get month => (origin as QueryMonthMarkedDatesProvider).month;
}

String _$queryDayMarkedDatesHash() =>
    r'1f4d1e27849db45c5a3fea76a45bdee89c273442';

/// See also [queryDayMarkedDates].
@ProviderFor(queryDayMarkedDates)
const queryDayMarkedDatesProvider = QueryDayMarkedDatesFamily();

/// See also [queryDayMarkedDates].
class QueryDayMarkedDatesFamily extends Family<AsyncValue<bool>> {
  /// See also [queryDayMarkedDates].
  const QueryDayMarkedDatesFamily();

  /// See also [queryDayMarkedDates].
  QueryDayMarkedDatesProvider call(
    DateTime day,
  ) {
    return QueryDayMarkedDatesProvider(
      day,
    );
  }

  @override
  QueryDayMarkedDatesProvider getProviderOverride(
    covariant QueryDayMarkedDatesProvider provider,
  ) {
    return call(
      provider.day,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'queryDayMarkedDatesProvider';
}

/// See also [queryDayMarkedDates].
class QueryDayMarkedDatesProvider extends AutoDisposeFutureProvider<bool> {
  /// See also [queryDayMarkedDates].
  QueryDayMarkedDatesProvider(
    DateTime day,
  ) : this._internal(
          (ref) => queryDayMarkedDates(
            ref as QueryDayMarkedDatesRef,
            day,
          ),
          from: queryDayMarkedDatesProvider,
          name: r'queryDayMarkedDatesProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$queryDayMarkedDatesHash,
          dependencies: QueryDayMarkedDatesFamily._dependencies,
          allTransitiveDependencies:
              QueryDayMarkedDatesFamily._allTransitiveDependencies,
          day: day,
        );

  QueryDayMarkedDatesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.day,
  }) : super.internal();

  final DateTime day;

  @override
  Override overrideWith(
    FutureOr<bool> Function(QueryDayMarkedDatesRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: QueryDayMarkedDatesProvider._internal(
        (ref) => create(ref as QueryDayMarkedDatesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        day: day,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<bool> createElement() {
    return _QueryDayMarkedDatesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is QueryDayMarkedDatesProvider && other.day == day;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, day.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin QueryDayMarkedDatesRef on AutoDisposeFutureProviderRef<bool> {
  /// The parameter `day` of this provider.
  DateTime get day;
}

class _QueryDayMarkedDatesProviderElement
    extends AutoDisposeFutureProviderElement<bool> with QueryDayMarkedDatesRef {
  _QueryDayMarkedDatesProviderElement(super.provider);

  @override
  DateTime get day => (origin as QueryDayMarkedDatesProvider).day;
}

String _$calendarControllerHash() =>
    r'41e5c031575ca80640d45e3e21d6d556a5482dcb';

abstract class _$CalendarController
    extends BuildlessAutoDisposeAsyncNotifier<List<CalendarState>> {
  late final DateTime day;

  FutureOr<List<CalendarState>> build(
    DateTime day,
  );
}

/// See also [CalendarController].
@ProviderFor(CalendarController)
const calendarControllerProvider = CalendarControllerFamily();

/// See also [CalendarController].
class CalendarControllerFamily extends Family<AsyncValue<List<CalendarState>>> {
  /// See also [CalendarController].
  const CalendarControllerFamily();

  /// See also [CalendarController].
  CalendarControllerProvider call(
    DateTime day,
  ) {
    return CalendarControllerProvider(
      day,
    );
  }

  @override
  CalendarControllerProvider getProviderOverride(
    covariant CalendarControllerProvider provider,
  ) {
    return call(
      provider.day,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'calendarControllerProvider';
}

/// See also [CalendarController].
class CalendarControllerProvider extends AutoDisposeAsyncNotifierProviderImpl<
    CalendarController, List<CalendarState>> {
  /// See also [CalendarController].
  CalendarControllerProvider(
    DateTime day,
  ) : this._internal(
          () => CalendarController()..day = day,
          from: calendarControllerProvider,
          name: r'calendarControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$calendarControllerHash,
          dependencies: CalendarControllerFamily._dependencies,
          allTransitiveDependencies:
              CalendarControllerFamily._allTransitiveDependencies,
          day: day,
        );

  CalendarControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.day,
  }) : super.internal();

  final DateTime day;

  @override
  FutureOr<List<CalendarState>> runNotifierBuild(
    covariant CalendarController notifier,
  ) {
    return notifier.build(
      day,
    );
  }

  @override
  Override overrideWith(CalendarController Function() create) {
    return ProviderOverride(
      origin: this,
      override: CalendarControllerProvider._internal(
        () => create()..day = day,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        day: day,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<CalendarController,
      List<CalendarState>> createElement() {
    return _CalendarControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CalendarControllerProvider && other.day == day;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, day.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin CalendarControllerRef
    on AutoDisposeAsyncNotifierProviderRef<List<CalendarState>> {
  /// The parameter `day` of this provider.
  DateTime get day;
}

class _CalendarControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<CalendarController,
        List<CalendarState>> with CalendarControllerRef {
  _CalendarControllerProviderElement(super.provider);

  @override
  DateTime get day => (origin as CalendarControllerProvider).day;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
