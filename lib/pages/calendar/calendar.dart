import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:table_calendar/table_calendar.dart';

import 'controller/calendar_controller.dart';
import 'widgets/calendar_event_list.dart';

class CalendarPage extends ConsumerStatefulWidget {
  const CalendarPage({super.key});

  @override
  ConsumerState<CalendarPage> createState() => _CalendarPageState();
}

class _CalendarPageState extends ConsumerState<CalendarPage> {
  DateTime _focusedDay = DateTime.now();

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        children: [
          TableCalendar(
            locale: context.locale.languageCode,
            firstDay: DateTime.utc(1990, 1, 1),
            lastDay: DateTime.now(),
            focusedDay: _focusedDay,
            headerStyle: const HeaderStyle(
              formatButtonVisible: false,
            ),
            selectedDayPredicate: (day) => isSameDay(_focusedDay, day),
            calendarStyle: CalendarStyle(
              isTodayHighlighted: false,
              selectedDecoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                shape: BoxShape.circle,
              ),
            ),
            calendarBuilders: CalendarBuilders(
              defaultBuilder: _calendarBuilder,
              todayBuilder: _calendarBuilder,
              selectedBuilder: _calendarSelectedBuilder,
            ),
            onDaySelected: _onDaySelected,
          ),
          Expanded(
            child: CalendarEventList(
              date: _focusedDay,
            ),
          ),
        ],
      ),
    );
  }

  Widget _calendarBuilder(BuildContext context, DateTime day, DateTime focusedDay) {
    return Center(child: Consumer(
      builder: (context, ref, child) {
        final localDay = DateTime(day.year, day.month, day.day);
        final dayMarked = ref.watch(queryDayMarkedDatesProvider(localDay));
        return dayMarked.when(
          data: (isMarked) => _buildDay(context, day, isMarked),
          error: (error, stack) => _buildDay(context, day, false),
          loading: () => _buildDay(context, day, false),
        );
      },
    ));
  }

  Widget _calendarSelectedBuilder(BuildContext context, DateTime day, DateTime focusedDay) {
    return Container(
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor,
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(day.day.toString(), style: const TextStyle(color: Colors.white)),
            Container(
              width: 4,
              height: 4,
              decoration: const BoxDecoration(
                color: Colors.transparent,
                shape: BoxShape.circle,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDay(BuildContext context, DateTime day, bool isMarked) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(day.day.toString()),
          Container(
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: isMarked ? Theme.of(context).primaryColor : Colors.transparent,
              shape: BoxShape.circle,
            ),
          ),
        ],
      ),
    );
  }

  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    setState(() {
      _focusedDay = focusedDay;
    });
  }
}
