import 'package:flower_timemachine/pages/calendar/controller/calendar_controller.dart';
import 'package:flower_timemachine/widgets/error_reporter.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/types/calendar_type.dart';

import 'calendar_event_item.dart';

class CalendarEventList extends ConsumerStatefulWidget {
  final DateTime date;

  const CalendarEventList({
    super.key,
    required this.date,
  });

  @override
  ConsumerState<CalendarEventList> createState() => _CalendarEventListState();
}

class _CalendarEventListState extends ConsumerState<CalendarEventList> {
  @override
  Widget build(BuildContext context) {
    final eventsProvider = ref.watch(calendarControllerProvider(widget.date));
    return eventsProvider.when(
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
      error: (error, stackTrace) => Center(
        child: ErrorReporter(error: error, stackTrace: stackTrace),
      ),
      data: (events) {
        if (events.isEmpty) {
          return Center(
            child: const Text('calendar_no_events').tr(),
          );
        }

        return ListView.builder(
          itemBuilder: (context, index) => _buildListViewItem(context, index, events),
        );
      },
    );
  }

  Widget? _buildListViewItem(BuildContext context, int index, List<CalendarState> events) {
    final controller = ref.read(calendarControllerProvider(widget.date).notifier);
    if (index >= events.length) {
      if (controller.hasMore) {
        controller.loadMore();
      }
      return null;
    }

    return CalendarEventItem(event: events[index]);
  }
}
