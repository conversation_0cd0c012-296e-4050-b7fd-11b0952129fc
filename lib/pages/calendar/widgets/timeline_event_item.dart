import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flower_timemachine/types/calendar_type.dart';
import 'package:flower_timemachine/pages/calendar/widgets/flower_info.dart';

class TimelineEventItem extends StatelessWidget {
  final CalendarState event;

  const TimelineEventItem({
    super.key,
    required this.event,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 文本内容
        if (event.timeline?.text != null && event.timeline!.text!.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              event.timeline!.text!,
              style: const TextStyle(fontSize: 16),
            ),
          ),

        // 照片网格
        if (event.photos != null && event.photos!.isNotEmpty) _buildPhotoGrid(context),

        // 花的信息
        FlowerInfo(flower: event.flower),
      ],
    );
  }

  Widget _buildPhotoGrid(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: GridView.count(
        crossAxisCount: 3,
        crossAxisSpacing: 8.0,
        mainAxisSpacing: 8.0,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        children: event.photos!
            .map(
              (photo) => Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8.0),
                  child: _buildPhoto(photo),
                ),
              ),
            )
            .toList(),
      ),
    );
  }

  Widget _buildPhoto(String photo) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8.0),
      child: Image(
        image: ResizeImage(
          FileImage(File(photo)),
          width: 160,
        ),
        width: 80,
        height: 80,
        fit: BoxFit.cover,
      ),
    );
  }
}
