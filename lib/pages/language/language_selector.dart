import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';

import 'controller/language_controller.dart';
import 'widgets/language_selector_item.dart';

class LanguageSelector extends ConsumerStatefulWidget {
  const LanguageSelector({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => LanguageSelectorState();

  static show(BuildContext context) {
    showMaterialModalBottomSheet(
      expand: false,
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => const LanguageSelector(),
    );
  }
}

class LanguageSelectorState extends ConsumerState<LanguageSelector> {
  @override
  Widget build(BuildContext context) {
    final supportedLocales = LanguageController.supportedLocales();
    return Material(
        clipBehavior: Clip.hardEdge,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        child: Safe<PERSON>rea(
            top: false,
            child: Container(
              constraints: const BoxConstraints(
                minHeight: 300
              ),
              padding: const EdgeInsets.only(left: 20, right: 20, top: 20, bottom: 10),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text("select_language").tr(),
                  const SizedBox(height: 30),
                  ListView.separated(
                    padding: EdgeInsets.zero,
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemBuilder: (context, index) => LanguageSelectorItem(locale: supportedLocales[index]),
                    separatorBuilder: (context, index) => const Divider(),
                    itemCount: supportedLocales.length
                  )
                ],
              ),
            )
        )
    );
  }
}