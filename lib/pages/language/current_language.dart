import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'controller/language_controller.dart';

class CurrentLanguage extends StatelessWidget {
  const CurrentLanguage({super.key});

  @override
  Widget build(BuildContext context) {
    final lan = LanguageController.languageCodeMapping(context.locale);

    if (lan == null) {
      return const Text("error").tr();
    } else {
      return Text(lan);
    }
  }
}
