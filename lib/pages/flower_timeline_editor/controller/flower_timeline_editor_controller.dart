import 'package:flower_timemachine/models/flower_timeline_editor_item.dart';
import 'package:flower_timemachine/models/media_item.dart';
import 'package:flower_timemachine/models/photo_record.dart';
import 'package:flower_timemachine/services/media_processing_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

part 'flower_timeline_editor_controller.g.dart';

@riverpod
class FlowerTimelineEditorController extends _$FlowerTimelineEditorController {
  // static const int maxText = 140;
  static const int maxImage = 9;

  // 媒体处理服务
  final _mediaService = MediaProcessingService();

  /// 处理选中的媒体资源
  Future<List<MediaItem>> processSelectedAssets(List<AssetEntity> assets) async {
    if (assets.isEmpty) return [];

    final mediaItems = await _mediaService.processSelectedAssets(
        assets,
    );

    return mediaItems;
  }

  /// 获取照片日期分组
  Future<Map<DateTime, List<AssetEntity>>> getPhotoDateGroups(List<AssetEntity> assets) async {
    // 收集所有照片的创建日期
    final Map<DateTime, List<AssetEntity>> dateGroups = {};

    for (final asset in assets) {
      // 获取照片创建日期
      final createDate = asset.createDateTime;

      // 按日期分组
      if (dateGroups.containsKey(createDate)) {
        dateGroups[createDate]!.add(asset);
      } else {
        dateGroups[createDate] = [asset];
      }
    }

    return dateGroups;
  }

  // 添加媒体项
  void addMediaItems(List<MediaItem> mediaItems) async {
    // 更新状态
    state = state.copyWith(
        mediaItems: [
          ...state.mediaItems,
          ...mediaItems
        ]
    );
  }

  // 添加单个媒体项
  void addMediaItem(MediaItem mediaItem) async {
    state = state.copyWith(
        mediaItems: [
          ...state.mediaItems,
          mediaItem
        ]
    );
  }

  // 兼容旧代码
  void addPhoto(String photo, {MediaType type = MediaType.image, String? thumbnailPath}) async {
    addMediaItem(MediaItem(
      path: photo,
      thumbnailPath: thumbnailPath,
      type: type,
    ));
  }

  // 删除媒体项
  void deleteMediaItem(MediaItem mediaItem) async {
    final oldMediaItems = state.mediaItems;

    state = state.copyWith(
      mediaItems: [
        for (final item in oldMediaItems)
          if (item.path != mediaItem.path) item
      ]
    );
  }

  String? get text => state.text;

  // 获取媒体项列表
  List<MediaItem> get mediaItems => List.unmodifiable(state.mediaItems);

  @override
  FlowerTimelineEditorItem build(FlowerTimelineEditorItem? item) {
    return item ?? FlowerTimelineEditorItem(
        text: null,
        mediaItems: [],
        dateTime: DateTime.now()
    );
  }

  /// 设置日期时间
  void setDateTime(DateTime dateTime) {
    state = state.copyWith(dateTime: dateTime);
  }
}