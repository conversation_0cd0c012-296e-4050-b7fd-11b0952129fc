import 'package:flower_timemachine/models/media_item.dart';
import 'package:flower_timemachine/widgets/media_renderer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:photo_view/photo_view.dart';


class FlowerTimelineEditorShowPhoto extends ConsumerStatefulWidget {
  const FlowerTimelineEditorShowPhoto({
    super.key,
    required this.photos,
    required this.initialPage,
  });

  static const routeName = 'flower_timeline_editor_show_photo';

  final List<MediaItem> photos;
  final int initialPage;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _ShowPhotoPageState();
}

class _ShowPhotoPageState extends ConsumerState<FlowerTimelineEditorShowPhoto> {
  late PageController pageController;
  int _currentPage = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          elevation: 0,
          backgroundColor: Colors.black,
        ),
        body: PhotoViewGallery.builder(
          pageController: pageController,
          scrollPhysics: const BouncingScrollPhysics(),
          gaplessPlayback: true,
          allowImplicitScrolling: true,
          builder: photoViewBuilder,
          itemCount: widget.photos.length,
          onPageChanged: (index) {
            setState(() {
              _currentPage = index; // 更新当前页面索引
            });
          },
        )
    );
  }

  PhotoViewGalleryPageOptions photoViewBuilder(BuildContext context, int index) {
    final item = widget.photos[index];
    return PhotoViewGalleryPageOptions.customChild(
      child: MediaRenderer(
        path: item.path,
        mediaType: item.type,
        isVisible: index == _currentPage, // 只有当前页面的视频才会播放
      ),
      minScale: PhotoViewComputedScale.contained,
      maxScale: PhotoViewComputedScale.covered * 2,

    );
  }

  @override
  void initState() {
    super.initState();
    _currentPage = widget.initialPage; // 初始化当前页面索引
    pageController = PageController(initialPage: widget.initialPage);
  }

  @override
  void dispose() {
    super.dispose();
    pageController.dispose();
  }
}
