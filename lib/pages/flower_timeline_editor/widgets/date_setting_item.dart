import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/date_utils.dart';
import 'package:flower_timemachine/models/flower_timeline_editor_item.dart';
import 'package:flower_timemachine/pages/flower_timeline_editor/controller/flower_timeline_editor_controller.dart';
import 'package:flower_timemachine/pages/flower_timeline_editor/widgets/setting_item.dart';
import 'package:flower_timemachine/widgets/date_picker_dialog.dart';
import 'package:flower_timemachine/widgets/ftm_line.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final _dateFormatter = DateFormat.yMMMd();
final _timeFormatter = DateFormat.Hm();

class FlowerTimelineEditorDateSettingItem extends ConsumerWidget {
  const FlowerTimelineEditorDateSettingItem({super.key, required this.timeline});

  final FlowerTimelineEditorItem? timeline;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final provider = flowerTimelineEditorControllerProvider(timeline);
    final controller = ref.read(provider.notifier);
    final dateTime = ref.watch(provider.select((f) => f.dateTime));

    return Column(children: [
      FlowerTimelineEditorSettingItem(
          text: "date".tr(),
          rightText: _dateFormatter.format(dateTime),
          onTap: () => onDateTap(context, ref, controller, dateTime)),
      const FTMLine(height: 1),
      FlowerTimelineEditorSettingItem(
          text: "time".tr(),
          rightText: _timeFormatter.format(dateTime),
          onTap: () => onTimeTap(context, ref, controller, dateTime)),
    ]);
  }

  void onDateTap(BuildContext context, WidgetRef ref, FlowerTimelineEditorController controller, DateTime dateTime) async {
    FocusScopeNode currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      FocusManager.instance.primaryFocus?.unfocus();
      // 等键盘收回去
      await Future.delayed(const Duration(milliseconds: 200));
    }

    if (!context.mounted) return;

    final newDate = await FTMDatePickerDialog.show(context, "confirm".tr(),
        defaultDate: dateTime, mode: CupertinoDatePickerMode.date, maximumDate: DateTime.now());

    if (newDate == null) {
      return;
    }

    final setDate = dateTime.copyWith(year: newDate.year, month: newDate.month, day: newDate.day);
    if (DateTime.now().isBefore(setDate)) {
      controller.setDateTime(DateTime.now());
    } else {
      controller.setDateTime(dateTime.copyWith(year: newDate.year, month: newDate.month, day: newDate.day));
    }
  }

  void onTimeTap(BuildContext context, WidgetRef ref, FlowerTimelineEditorController controller, DateTime dateTime) async {
    FocusScopeNode currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      FocusManager.instance.primaryFocus?.unfocus();
      // 等键盘收回去
      await Future.delayed(const Duration(milliseconds: 200));
    }

    if (!context.mounted) return;

    // 如果日期小于今天就不限制时间
    DateTime? maximumDate;
    if (getTodayZeroClock().isBefore(dateTime)) {
      maximumDate = DateTime.now();
    }

    final newDate = await FTMDatePickerDialog.show(context, "confirm".tr(),
        defaultDate: dateTime, mode: CupertinoDatePickerMode.time, maximumDate: maximumDate);

    if (newDate == null) {
      return;
    }

    controller.setDateTime(dateTime.copyWith(hour: newDate.hour, minute: newDate.minute, second: newDate.second));
  }
}
