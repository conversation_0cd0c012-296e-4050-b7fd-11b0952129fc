import 'dart:ui';

import 'package:flutter/material.dart';

class FlowerTimelineEditorSettingItem extends StatelessWidget {
  final String text;
  final String rightText;
  final GestureTapCallback onTap;

  const FlowerTimelineEditorSettingItem({super.key, required this.text, required this.rightText, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          height: 50,
          child: Row(children: [
            Text(text, style: const TextStyle(fontSize: 18, fontFeatures: [FontFeature.tabularFigures()])),
            const Spacer(),
            Text(rightText, style: const TextStyle(fontSize: 18, fontFeatures: [FontFeature.tabularFigures()])),
            const SizedBox(width: 3),
            const Icon(Icons.arrow_forward_ios, size: 16)
          ])),
    );
  }
}
