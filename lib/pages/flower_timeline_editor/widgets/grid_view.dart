import 'package:dotted_border/dotted_border.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/controller/user_controller.dart';
import 'package:flower_timemachine/models/media_item.dart';
import 'package:flower_timemachine/models/photo_record.dart';
import 'package:flower_timemachine/pages/flower_timeline_editor/controller/flower_timeline_editor_controller.dart';
import 'package:flower_timemachine/pages/flower_timeline_editor/show_photo.dart';
import 'package:flower_timemachine/pages/flower_timeline_editor/widgets/photo_date_picker_sheet.dart';
import 'package:flower_timemachine/widgets/loading_dialog.dart';
import 'package:flower_timemachine/widgets/media_preview/media_preview.dart';
import 'package:flower_timemachine/widgets/permission_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pull_down_button/pull_down_button.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class FlowerTimelineEditorGridView extends StatefulWidget {
  const FlowerTimelineEditorGridView({super.key, required this.controller});

  final FlowerTimelineEditorController controller;

  @override
  State<FlowerTimelineEditorGridView> createState() => _FlowerTimelineEditorGridViewState();
}

class _FlowerTimelineEditorGridViewState extends State<FlowerTimelineEditorGridView> {
  @override
  Widget build(BuildContext context) {
    return GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 4,
          mainAxisSpacing: 4,
        ),
        shrinkWrap: true,
        itemBuilder: _buildPhotoItem,
        itemCount: widget.controller.mediaItems.length + 1);
  }

  Widget? _buildPhotoItem(BuildContext context, int index) {
    if (index > widget.controller.mediaItems.length) {
      return null;
    }
    if (index == widget.controller.mediaItems.length) {
      if (widget.controller.mediaItems.length >= FlowerTimelineEditorController.maxImage) {
        return null;
      }
      // 添加更多图片按钮
      return _buildAddMoreButton();
    }

    final mediaItem = widget.controller.mediaItems[index];

    return Stack(
      fit: StackFit.expand,
      children: [
        GestureDetector(
          onTap: () => onTapPhoto(context, mediaItem),
          child: MediaPreview(
            path: mediaItem.thumbnailPath ?? mediaItem.path,
            mediaType: mediaItem.type,
            width: 128,
          ),
        ),
        Positioned(
            right: 0,
            top: 0,
            child: GestureDetector(
                onTap: () => onDeletePhoto(mediaItem),
                child: Container(
                  width: 25,
                  height: 25,
                  alignment: Alignment.center,
                  color: Colors.black45,
                  child: const Icon(Icons.close, color: Colors.white),
                )))
      ],
    );
  }

  Widget _buildAddMoreButton() {
    return PullDownButton(
      itemBuilder: (context) => [
        PullDownMenuItem(
          title: 'from_album'.tr(),
          onTap: _byPhotoAlbum,
        ),
        PullDownMenuItem(
          title: 'from_camera'.tr(),
          onTap: _byCamera,
        ),
      ],
      buttonBuilder: (context, showMenu) => DottedBorder(
        color: Colors.black26,
        child: GestureDetector(
          onTap: showMenu,
          child: Center(
            child: LayoutBuilder(
              builder: (context, constraint) => Icon(
                Icons.add,
                size: constraint.biggest.height / 2,
                color: Colors.black26,
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _byPhotoAlbum() async {
    final permission = await PhotoManager.requestPermissionExtend();
    if (permission != PermissionState.limited && permission != PermissionState.authorized) {
      if (mounted) {
        await PermissionDialog.show(context, "flower_timeline_editor.add_image_permission".tr());
      }
      return;
    }

    final photoNumber = widget.controller.mediaItems.length;
    final photoLimit = FlowerTimelineEditorController.maxImage - photoNumber;

    if (photoLimit <= 0) {
      if (mounted) {
        Fluttertoast.showToast(
            msg: "flower_timeline_editor.image_max_limit"
                .tr(namedArgs: {"number": FlowerTimelineEditorController.maxImage.toString()}),
            toastLength: Toast.LENGTH_SHORT);
      }
      return;
    }

    if (!mounted) {
      return;
    }

    // 使用 wechat_assets_picker 选择媒体文件
    final List<AssetEntity>? result = await AssetPicker.pickAssets(
      context,
      pickerConfig: AssetPickerConfig(
        maxAssets: photoLimit,
        requestType: RequestType.common, // 同时支持图片和视频
        specialPickerType: photoLimit == 1 ? SpecialPickerType.noPreview : null,
        themeColor: Theme.of(context).primaryColor,
        limitedPermissionOverlayPredicate: (permissionState) => false
      ),
    );

    if (!mounted || result == null || result.isEmpty) {
      return;
    }

    LoadingDialog.show();
    try {
      // 处理选中的媒体资源
      final mediaItems = await widget.controller.processSelectedAssets(result);
      widget.controller.addMediaItems(mediaItems);

      LoadingDialog.dismiss();

      // 检查是否需要自动识别照片日期时间
      if (UserController.get().isVip() &&
          ShareConfig.getAutoDetectPhotoDate() &&
          mediaItems.isNotEmpty &&
          mounted) {
        // 获取照片日期分组
        final dateGroups = await widget.controller.getPhotoDateGroups(result);

        final dates = dateGroups.entries.map((entry) =>
            PhotoDateInfo(
              dateTime: entry.key,
              count: entry.value.length,
            )
        ).toList();

        // 按日期排序
        dates.sort((a, b) => b.dateTime.compareTo(a.dateTime));

        // 显示日期选择底部弹窗
        if (mounted) {
          final selectedDate = await PhotoDatePickerSheet.show(
              context, dates
          );

          if (selectedDate != null && mounted) {
            widget.controller.setDateTime(selectedDate);
            return;
          }
        }
      }
    } finally {
      if (LoadingDialog.isShowing()) {
        LoadingDialog.dismiss();
      }
    }
  }

  void _byCamera() async {
    XFile? ret;
    try {
      LoadingDialog.show();
      final ImagePicker picker = ImagePicker();
      ret = await picker.pickImage(source: ImageSource.camera);
    } on PlatformException catch (e) {
      if (e.code == 'camera_access_denied') {
        if (mounted) {
          PermissionDialog.show(context, "flower_timeline_editor.camera_permission".tr());
        }
      }
      return;
    } finally {
      LoadingDialog.dismiss();
    }

    if (ret == null) {
      return;
    }

    if (widget.controller.mediaItems.length + 1 > FlowerTimelineEditorController.maxImage) {
      if (mounted) {
        Fluttertoast.showToast(
            msg: "flower_timeline_editor.image_max_limit"
                .tr(namedArgs: {"number": FlowerTimelineEditorController.maxImage.toString()}),
            toastLength: Toast.LENGTH_SHORT);
      }
      return;
    }

    widget.controller.addPhoto(ret.path, type: MediaType.image);
  }

  void onDeletePhoto(MediaItem mediaItem) async {
    widget.controller.deleteMediaItem(mediaItem);
  }

  void onTapPhoto(BuildContext context, MediaItem mediaItem) async {
    FocusManager.instance.primaryFocus?.unfocus();
    // 等键盘收回去
    await Future.delayed(const Duration(milliseconds: 200));

    final photos = widget.controller.mediaItems;
    int initialPage = photos.indexOf(mediaItem);
    if (initialPage < 0) {
      initialPage = 0;
    }

    if (context.mounted) {
      await Navigator.pushNamed(
          context,
          FlowerTimelineEditorShowPhoto.routeName,
          arguments: [widget.controller.mediaItems, initialPage]
      );
    }
  }
}
