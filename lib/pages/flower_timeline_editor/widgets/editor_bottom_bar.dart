import 'package:flutter/material.dart';

class FlowerTimelineEditorBottomBar extends StatelessWidget {
  const FlowerTimelineEditorBottomBar({super.key, required this.currentTextNum});

  final ValueNotifier<int> currentTextNum;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      alignment: Alignment.centerRight,
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: ValueListenableBuilder(
        valueListenable: currentTextNum,
        builder: (BuildContext context, int value, Widget? child) =>
          Text('$value')
      ),
    );
  }
}