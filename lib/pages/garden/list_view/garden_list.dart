import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/controller/flower_list_controller.dart';
import 'package:flower_timemachine/controller/tag_list_controller.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/pages/garden/widgets/empty.dart';
import 'package:flower_timemachine/widgets/error_reporter.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import 'garden_info_card_widget.dart';

class GardenList extends ConsumerStatefulWidget {
  const GardenList({super.key});

  @override
  ConsumerState<GardenList> createState() => _GardenListState();
}

class _GardenListState extends ConsumerState<GardenList> {
  final GlobalKey<AnimatedListState> _gardenListKey = GlobalKey<AnimatedListState>();

  @override
  Widget build(BuildContext context) {
    return ref.watch(flowerListLoaderProvider).when(
        error: (error, stackTrace) => ErrorReporter(error: error, stackTrace: stackTrace),
        loading: () => const Center(child: SizedBox(height: 100, width: 100, child: CircularProgressIndicator())),
        data: (_) => _buildAnimatedList(ref.read(flowerListControllerProvider).data));
  }

  Widget _buildAnimatedList(List<Flower> data) {
    if (data.isNotEmpty) {
      return AnimatedList(key: _gardenListKey, itemBuilder: _buildAnimatedListItem, initialItemCount: data.length + 1);
    }

    if (ref.read(currentTagProvider).id != allTag.id) {
      return Empty(message: 'garden_tag.no_flower'.tr());
    } else {
      return Empty(
        message: 'garden_tag.garden_empty'.tr(),
        subMessage: 'garden_tag.first_flower'.tr(),
      );
    }
  }

  Widget _buildAnimatedListItem(BuildContext context, int index, Animation<double> animation) {
    final controller = ref.read(flowerListControllerProvider);
    if (index == controller.length - 1) {
      if (controller.hasMore) {
        _loadMore();
      }
    } else if (index == controller.length) {
      return _buildFlowerNumberWidget(controller.length);
    } else if (index > controller.length) {
      // 不知如何，有时候会出现大于 controller.length 的情况
      return const SizedBox.shrink();
    }

    try {
      final flower = controller.get(index);
      return SizeTransition(
        sizeFactor: animation,
        child: GardenInfoCardWidget(flower: flower!),
      );
    } catch (e) {
      Sentry.captureMessage("build item 大于花的总数: item index: $index, controller length: ${controller.length}");
      rethrow;
    }
  }

  Widget _buildFlowerNumberWidget(int length) {
    return Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Center(child: const Text("garden_tag.flower_number").tr(namedArgs: {"number": length.toString()})));
  }

  Future<void> _loadMore() async {
    final controller = ref.read(flowerListControllerProvider);
    final newData = await controller.load();
    if (newData.isNotEmpty) {
      final insertIndex = controller.length - newData.length;
      _gardenListKey.currentState?.insertAllItems(insertIndex, newData.length);
    }
  }

  void _onAddFlower(Flower flower) {
    final controller = ref.read(flowerListControllerProvider);

    final insertIndex = controller.addFlower(flower);
    if (controller.length == 1) {
      ref.invalidate(flowerListControllerProvider);
      return;
    }

    _gardenListKey.currentState?.insertItem(insertIndex, duration: const Duration(milliseconds: 500));
  }

  void _onDeleteFlower(Flower flower) {
    final controller = ref.read(flowerListControllerProvider);
    final index = controller.findFlower(flower);
    if (index < 0) {
      return;
    }

    if (controller.length == 1) {
      controller.deleteByIndex(index);
      ref.invalidate(flowerListControllerProvider);
      return;
    }

    final item = GardenInfoCardWidget(flower: flower);
    controller.deleteByIndex(index);

    _gardenListKey.currentState?.removeItem(index, (context, animation) {
      return SizeTransition(
        sizeFactor: animation,
        child: item,
      );
    }, duration: const Duration(milliseconds: 500));
  }

  void _onMoveFlower(Flower flower) {
    final controller = ref.read(flowerListControllerProvider);
    if (controller.length == 1) {
      return;
    }

    final index = controller.sortFlower(flower);
    if (index == null) {
      return;
    }
    final (oldIndex, newIndex) = index;
    if (oldIndex == newIndex) {
      return;
    }

    final item = GardenInfoCardWidget(flower: flower);
    controller.deleteByIndex(oldIndex);
    _gardenListKey.currentState?.removeItem(oldIndex, (context, animation) {
      return SizeTransition(
        sizeFactor: animation,
        child: item,
      );
    }, duration: const Duration(milliseconds: 500));

    Future.delayed(const Duration(milliseconds: 600), () {
      controller.insertByIndex(newIndex, flower);
      _gardenListKey.currentState?.insertItem(newIndex);
    });
  }

  void _onFlowerListEvent() {
    final event = flowerListControllerEventProvider.value;
    if (event == null) {
      return;
    }

    event.when(addFlower: _onAddFlower, deleteFlower: _onDeleteFlower, moveFlower: _onMoveFlower);
  }

  @override
  void initState() {
    super.initState();
    flowerListControllerEventProvider.addListener(_onFlowerListEvent);
  }

  @override
  void dispose() {
    flowerListControllerEventProvider.removeListener(_onFlowerListEvent);
    super.dispose();
  }
}
