import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class Empty extends StatelessWidget {
  final String message;
  final String? subMessage;

  const Empty({
    super.key,
    required this.message,
    this.subMessage,
  });

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: width * 0.4,
            height: width * 0.4,
            decoration: const BoxDecoration(
              color: Color(0xFFEEFDF2),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: FaIcon(
                FontAwesomeIcons.seedling,
                color: const Color(0xFF41D975),
                size: width * 0.2,
              ),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: width * 0.04,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF39424F),
            ),
            textAlign: TextAlign.center,
          ),
          if (subMessage != null)
            Padding(
              padding: EdgeInsets.only(top: width * 0.02),
              child: Text(
                subMessage!,
                style: TextStyle(
                  fontSize: width * 0.03,
                  color: const Color(0xFF909399),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
