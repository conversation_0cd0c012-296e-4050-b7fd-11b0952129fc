import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/resources.dart';
import 'package:flower_timemachine/controller/flower_list_controller.dart';
import 'package:flower_timemachine/controller/flower_pending_auto_nurture_controller.dart';
import 'package:flower_timemachine/controller/garden_controller.dart';
import 'package:flower_timemachine/controller/tag_list_controller.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/pages/flower_detail/flower_detail.dart';
import 'package:flower_timemachine/pages/garden/widgets/empty.dart';
import 'package:flower_timemachine/widgets/error_reporter.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class GardenGird extends ConsumerStatefulWidget {
  const GardenGird({super.key});

  @override
  ConsumerState<GardenGird> createState() => _GardenGirdState();
}

class _GardenGirdState extends ConsumerState<GardenGird> {
  bool _isAutoNurtureEnabled = false;
  late List<Flower> _pendingAutoNurtureList;

  @override
  Widget build(BuildContext context) {
    final appNavigationState = ref.watch(appNavigationStateProvider);
    _isAutoNurtureEnabled = appNavigationState == AppNavigationState.autoNurture;
    _pendingAutoNurtureList = ref.watch(flowerPendingAutoNurtureControllerProvider);

    return ref.watch(flowerListLoaderProvider).when(
        error: (error, stackTrace) => ErrorReporter(error: error, stackTrace: stackTrace),
        loading: () => const Center(child: SizedBox(height: 100, width: 100, child: CircularProgressIndicator())),
        data: (_) => _buildGrid(ref.read(flowerListControllerProvider).data));
  }

  Widget _buildGrid(List<Flower> data) {
    if (data.isEmpty) {
      if (ref.read(currentTagProvider).id != allTag.id) {
        return Empty(message: 'garden_tag.no_flower'.tr());
      } else {
        return Empty(
          message: 'garden_tag.garden_empty'.tr(),
          subMessage: 'garden_tag.first_flower'.tr(),
        );
      }
    }

    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        mainAxisSpacing: 16,
        crossAxisSpacing: 2,
      ),
      itemBuilder: (BuildContext context, int index) => _buildGridItem(context, index, data),
    );
  }

  Widget? _buildGridItem(BuildContext context, int index, List<Flower> data) {
    final controller = ref.read(flowerListControllerProvider);
    if (index >= data.length) {
      if (controller.hasMore) {
        _loadMore();
      }
      return null;
    }

    final flower = data[index];
    final widget = Column(
      children: [
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  spreadRadius: 4,
                  blurRadius: 4,
                  offset: const Offset(0, 0),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Container(
                color: Colors.white,
                child: flower.avatar != null
                    ? Image.file(File(flower.avatar!), fit: BoxFit.cover)
                    : Image.asset(R.iconFlower, fit: BoxFit.cover),
              ),
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          flower.name,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: const TextStyle(fontSize: 14),
        ),
      ],
    );

    if (_isAutoNurtureEnabled) {
      return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () => _onTapAutoNurture(flower),
        child: IgnorePointer(child: _buildAutoNurtureModeWidget(widget, flower)),
      );
    } else {
      return GestureDetector(
        onTap: () => _onTap(flower),
        child: widget,
      );
    }
  }

  Widget _buildAutoNurtureModeWidget(Widget child, Flower flower) {
    return Stack(
      children: [
        Positioned.fill(child: child),
        Positioned(
          top: 4,
          left: 12,
          child: SizedBox(
            width: 24,
            height: 24,
            child: Checkbox(
              shape: const CircleBorder(),
              value: _pendingAutoNurtureList.contains(flower),
              onChanged: (v) {},
            ),
          ),
        )
      ],
    );
  }

  void _onTap(Flower flower) async {
    final resultRef = FlowerDetailResultRef();
    await Navigator.pushNamed(context, FlowerDetailPage.name, arguments: [flower, resultRef]);

    final result = resultRef.result;
    if (result != null) {
      final _ = ref.invalidate(flowerListControllerProvider);
    }
  }

  void _onFlowerListEvent() {
    final event = flowerListControllerEventProvider.value;
    if (event == null) {
      return;
    }

    final _ = ref.invalidate(flowerListControllerProvider);
  }

  void _onTapAutoNurture(Flower flower) {
    final controller = ref.read(flowerPendingAutoNurtureControllerProvider.notifier);
    if (_pendingAutoNurtureList.contains(flower)) {
      controller.remove(flower);
    } else {
      controller.add(flower);
    }
  }

  Future<void> _loadMore() async {
    final controller = ref.read(flowerListControllerProvider);
    final newData = await controller.load();
    if (newData.isNotEmpty) {
      setState(() {});
    }
  }

  @override
  void initState() {
    super.initState();

    flowerListControllerEventProvider.addListener(_onFlowerListEvent);
  }

  @override
  void dispose() {
    flowerListControllerEventProvider.removeListener(_onFlowerListEvent);
    super.dispose();
  }
}
