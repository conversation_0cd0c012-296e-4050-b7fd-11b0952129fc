import 'dart:async';

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/controller/user_controller.dart';
import 'package:flower_timemachine/models/transaction.dart';
import 'package:flower_timemachine/network/app_purchase.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

part 'app_purchase_controller.g.dart';


@riverpod
Future<ProductDetailsResponse> productDetails(ProductDetailsRef ref) async {
  const Set<String> _kIds = <String>{'flowertime_vip'};
  return await InAppPurchase.instance.queryProductDetails(_kIds);
}

class AppPurchaseController {
  final completer = Completer<int>();

  FutureOr<bool> buyVip(ProductDetails detail, String userId) async {
    final PurchaseParam purchaseParam = PurchaseParam(
      productDetails: detail,
      applicationUserName: userId
    );

    return await InAppPurchase.instance.buyNonConsumable(purchaseParam: purchaseParam);
  }

  FutureOr<void> restore(String userId) async {
    await InAppPurchase.instance.restorePurchases();
  }

  FutureOr<bool> verifyPurchase(
      String? productID,
      String localVerificationData,
      String serverVerificationData,
      String userId
      ) async {
    // 写到数据库，防止网络问题
    final transaction = await Transaction.create(
        productID, localVerificationData, serverVerificationData
    );
    try {
      // 如果服务器返回校验失败则返回失败
      final resp = await AppPurchase.verify(userId, productID, serverVerificationData, DateTime.now());
      if (resp.isVerify) {
        await transaction.verify();
      }

      return resp.isVerify;
    } on DioException catch (error, stacktrace) {
      if (error.type != DioExceptionType.connectionError
          || error.type == DioExceptionType.connectionTimeout) {
        Sentry.captureException(error, stackTrace: stacktrace);
      }
      print("校验失败：$error");
      // 如果网络超时或其他异常返回成功，等后面继续校验
    }

    return true;
  }

  Future<String?> checkVipValidity() async {
    final transactions = await Transaction.getAll();
    final userController = UserController.get();
    final isVip = userController.isVip();

    try {
      final resp = await AppPurchase.checkVipValidity(isVip, userController.getID(), transactions);
      // 如果 vip 状态没有变化不需要做什么
      if (isVip == resp.isVip) {
        return null;
      }

      // 从非 vip 变成 vip，可能是服务端发生变化？也不做什么提示，但将改为为会员状态
      if (isVip == false && resp.isVip == true) {
        await userController.setVip(true);
        return null;
      }

      // 这里就是 isVip == true && resp.isVip == false 了
      if (resp.refund) {
        await userController.setVip(false);
        return "vip_page.refuse_message".tr();
      } else if (resp.failedVerify) {
        await userController.setVip(false);
        return "vip_page.invalid_message".tr();
      }
    } on DioException catch (e, s) {
      if (e.type != DioExceptionType.connectionError || e.type == DioExceptionType.connectionTimeout) {
        Sentry.captureException(e, stackTrace: s);
      }
      print("checkVipValidity 异常 $e, $s");
    }

    return null;
  }
}

final appPurchaseControllerProvider = Provider((ref) => AppPurchaseController());
