// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'flower_nurture_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$flowerNurtureHash() => r'3a6811696a9a1a3dee6204aaa759e8296d881206';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [flowerNurture].
@ProviderFor(flowerNurture)
const flowerNurtureProvider = FlowerNurtureFamily();

/// See also [flowerNurture].
class FlowerNurtureFamily extends Family<AsyncValue<List<NurtureType>>> {
  /// See also [flowerNurture].
  const FlowerNurtureFamily();

  /// See also [flowerNurture].
  FlowerNurtureProvider call(
    Flower flower,
  ) {
    return FlowerNurtureProvider(
      flower,
    );
  }

  @override
  FlowerNurtureProvider getProviderOverride(
    covariant FlowerNurtureProvider provider,
  ) {
    return call(
      provider.flower,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'flowerNurtureProvider';
}

/// See also [flowerNurture].
class FlowerNurtureProvider
    extends AutoDisposeFutureProvider<List<NurtureType>> {
  /// See also [flowerNurture].
  FlowerNurtureProvider(
    Flower flower,
  ) : this._internal(
          (ref) => flowerNurture(
            ref as FlowerNurtureRef,
            flower,
          ),
          from: flowerNurtureProvider,
          name: r'flowerNurtureProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$flowerNurtureHash,
          dependencies: FlowerNurtureFamily._dependencies,
          allTransitiveDependencies:
              FlowerNurtureFamily._allTransitiveDependencies,
          flower: flower,
        );

  FlowerNurtureProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.flower,
  }) : super.internal();

  final Flower flower;

  @override
  Override overrideWith(
    FutureOr<List<NurtureType>> Function(FlowerNurtureRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FlowerNurtureProvider._internal(
        (ref) => create(ref as FlowerNurtureRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        flower: flower,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<NurtureType>> createElement() {
    return _FlowerNurtureProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FlowerNurtureProvider && other.flower == flower;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, flower.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin FlowerNurtureRef on AutoDisposeFutureProviderRef<List<NurtureType>> {
  /// The parameter `flower` of this provider.
  Flower get flower;
}

class _FlowerNurtureProviderElement
    extends AutoDisposeFutureProviderElement<List<NurtureType>>
    with FlowerNurtureRef {
  _FlowerNurtureProviderElement(super.provider);

  @override
  Flower get flower => (origin as FlowerNurtureProvider).flower;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
