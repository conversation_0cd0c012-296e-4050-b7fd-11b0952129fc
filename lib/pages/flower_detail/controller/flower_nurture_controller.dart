import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/repository/monthly_nurture_cycle_repo.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/controller/nurture_types_controller.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'flower_nurture_controller.g.dart';

@riverpod
Future<List<NurtureType>> flowerNurture(FlowerNurtureRef ref, Flower flower) async {
  final monthlyCycles = await MonthlyNurtureCycleRepository.getFlowerMonthlyCycles(flower.id);
  final nurtureTypesController = NurtureTypesController.get();
  final now = DateTime.now();
  final currentMonth = now.month;

  for (final type in nurtureTypesController.types) {
    if (!type.enable) {
      continue;
    }

    
  }


  final List<NurtureType> types = [];
  for (final entry in monthlyCycles.typesCycles.entries) {
    final typeId = entry.key;
    final nurtureType = nurtureTypesController.getTypeInfo(typeId);
    if (nurtureType == null || !nurtureType.enable) {
      continue;
    }

    final effectiveCycle = monthlyCycles.getEffectiveCycleForTypeAndMonth(nurtureType, currentMonth);
    if (effectiveCycle > 0) {
      types.add(nurtureType);
    }
  }

  return types;
}
