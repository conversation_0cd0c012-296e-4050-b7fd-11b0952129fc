import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/date_utils.dart';
import 'package:flower_timemachine/common/dialog_utils.dart';
import 'package:flower_timemachine/controller/nurture_list_controller.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/models/maintenance_record.dart';
import 'package:flower_timemachine/pages/flower_detail/flower_detail.dart';
import 'package:flower_timemachine/widgets/error_reporter.dart';
import 'package:flower_timemachine/widgets/timeline_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';

class NurtureRecordsWidget extends ConsumerStatefulWidget {
  final Flower flower;
  final ValueNotifier<bool> editModel;
  final FlowerDetailResultRef resultRef;

  const NurtureRecordsWidget({super.key, required this.flower, required this.editModel, required this.resultRef});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _NurtureRecordsWidgetState();
}

class _NurtureRecordsWidgetState extends ConsumerState<NurtureRecordsWidget> {
  @override
  Widget build(BuildContext context) => ref.watch(nurtureListControllerControllerProvider(widget.flower)).when(
      error: (error, stackTrace) => ErrorReporter(error: error, stackTrace: stackTrace),
      loading: () => const Center(child: SizedBox(height: 20, width: 20, child: CircularProgressIndicator())),
      data: (items) {
        return items.isEmpty
            ? Container(
                alignment: Alignment.center,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: const Text(
                  'nurture.first_care',
                  style: TextStyle(color: Colors.black38),
                ).tr())
            : TimelineWidget(itemBuilder: _buildItem);
      });

  TimelineModel? _buildItem(BuildContext context, int index) {
    final controller = ref.read(nurtureListControllerControllerProvider(widget.flower).notifier);
    if (index >= controller.items.length) {
      if (controller.hasMore) {
        controller.loadMore();
      }
      return null;
    }

    final item = controller.items[index];

    Widget icon = SvgPicture.asset(item.type.icon, width: 15, height: 15);
    String title = item.type.name;
    String titleWithRemark = title;
    if (item.remark?.isNotEmpty ?? false) {
      titleWithRemark += ' - ${item.remark}';
    }

    final dateTime = DateTime.fromMillisecondsSinceEpoch(item.time * 1000);

    return TimelineModel(
      date: dateTime,
      datePrompt: calcDatePrompt(dateTime, DateTime.now()),
      child: ValueListenableBuilder<bool>(
          valueListenable: widget.editModel,
          builder: (BuildContext context, bool editModel, Widget? child) {
            final Widget ret;
            final List<Widget> rowChildren = [];
            final child = Padding(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: Row(children: rowChildren),
            );

            if (editModel) {
              rowChildren.addAll(
                  [const Icon(Icons.delete_rounded, color: Colors.black45, size: 18), const SizedBox(width: 5)]);

              ret = InkWell(
                child: child,
                onTap: () {
                  onDelete(item, dateTime, title);
                },
              );
            } else {
              ret = child;
            }

            rowChildren.addAll([
              const SizedBox(width: 8),
              icon,
              const SizedBox(width: 4),
              Flexible(child: Text(titleWithRemark)),
            ]);

            return ret;
          }),
    );
  }

  Future<void> onDelete(MaintenanceRecord record, DateTime dateTime, String type) async {
    final dateFormat = DateFormat.MMMd(context.locale.languageCode);

    final desc = 'nurture.delete_alert'.tr(namedArgs: {"date": dateFormat.format(dateTime), "type": type});
    final isDel = await showAlertDialog('alert'.tr(), desc, context);

    if (!(isDel ?? false)) {
      return;
    }

    final controller = ref.read(nurtureListControllerControllerProvider(widget.flower).notifier);
    final ret = await controller.deleteRecord(record);
    if (ret <= 0) {
      Fluttertoast.showToast(msg: "system_error_and_retry".tr(), toastLength: Toast.LENGTH_LONG);
    }

    widget.resultRef.result = FlowerDetailResult.edited;
  }
}
