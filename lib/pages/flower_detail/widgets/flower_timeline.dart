import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/controller/flower_timeline_controller.dart';
import 'package:flower_timemachine/models/flower_timeline_item.dart';
import 'package:flower_timemachine/models/media_item.dart';
import 'package:flower_timemachine/models/photo_record.dart';
import 'package:flower_timemachine/widgets/loading_dialog.dart';
import 'package:flower_timemachine/widgets/media_preview/media_preview.dart';
import 'package:flower_timemachine/widgets/timeline_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:ui' as ui;

import '../../../common/dialog_utils.dart';
import '../../../models/flower.dart';
import '../../../models/flower_timeline_editor_item.dart';
import '../../../widgets/error_reporter.dart';
import '../../../widgets/ftm_box.dart';
import '../../flower_timeline_editor/flower_timeline_editor.dart';

class FlowerTimelineWidget extends ConsumerStatefulWidget {
  final Flower flower;
  final ValueNotifier<bool> editModel;

  const FlowerTimelineWidget({super.key, required this.flower, required this.editModel});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _FlowerTimelineState();
}

class _FlowerTimelineState extends ConsumerState<FlowerTimelineWidget> {
  final timeFormatter = DateFormat.Hm();

  @override
  Widget build(BuildContext context) {
    return ref.watch(flowerTimelineControllerProvider(widget.flower)).when(
        error: (error, stackTrace) => ErrorReporter(error: error, stackTrace: stackTrace),
        loading: () => const Center(child: SizedBox(height: 20, width: 20, child: CircularProgressIndicator())),
        data: (items) {
          return items.isEmpty
              ? Center(child: Text('timeline.first_photo'.tr(), style: const TextStyle(color: Colors.black38)))
              : TimelineWidget(itemBuilder: _buildItem);
        });
  }

  TimelineModel? _buildItem(BuildContext context, int index) {
    final controller = ref.read(flowerTimelineControllerProvider(widget.flower).notifier);
    if (index >= controller.items.length) {
      final controller = ref.read(flowerTimelineControllerProvider(widget.flower).notifier);
      if (controller.hasMore) {
        controller.loadMore();
      }
      return null;
    }

    final item = controller.items[index];

    final List<Widget> children = [];

    for (final photo in item.photos) {
      // 根据媒体类型创建不同的渲染组件
      final photoWidget = InkWell(
        onTap: () {
          // 使用新的 MediaViewer 查看媒体文件
          Navigator.pushNamed(context, 'show_photo', arguments: [photo, controller.items, widget.flower.id]);
        },
        child: FTMBox(
          circular: 5,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(5.0),
            child: LayoutBuilder(
              builder: (BuildContext context, BoxConstraints constraints) {
                return SizedBox(
                  width: constraints.maxWidth,
                  height: constraints.minHeight,
                  child: MediaPreview(
                    path: photo.displayPath,
                    mediaType: photo.mediaType,
                    width: constraints.maxWidth,
                  )
                );
              },
            ),
          ),
        ),
      );

      children.add(photoWidget);
    }

    Widget image = GridView.count(
      crossAxisCount: 3,
      padding: EdgeInsets.zero,
      childAspectRatio: 1.0,
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      children: children,
    );

    final formatTime = timeFormatter.format(item.dateTime);

    // 8 个像素是 const SizedBox(height: 8) padding
    final timeTextHeight = _getTextHeight(formatTime);

    Widget timeWidget = Text(formatTime, style: const TextStyle(color: Colors.black26));
    timeWidget = ValueListenableBuilder(
        valueListenable: widget.editModel,
        builder: (BuildContext context, bool value, Widget? text) {
          Widget child;
          if (value) {
            child = Row(
              children: [
                text!,
                const Spacer(),
                GestureDetector(
                    onTap: () => _onEdit(item), child: Icon(Icons.edit, size: timeTextHeight, color: Colors.black45)),
                const SizedBox(width: 12),
                GestureDetector(
                    onTap: () => _onDelete(item),
                    child: Icon(Icons.delete, size: timeTextHeight, color: Colors.black45)),
              ],
            );
          } else {
            child = text!;
          }

          return Padding(padding: const EdgeInsets.symmetric(horizontal: 5), child: child);
        },
        child: timeWidget);

    Widget? textWidget;
    if (item.text?.isNotEmpty ?? false) {
      textWidget = Padding(
        padding: const EdgeInsets.symmetric(horizontal: 5),
        child: Text(item.text!),
      );
    }

    return TimelineModel(
        date: item.dateTime,
        datePrompt: item.datePrompt,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [if (textWidget != null) textWidget, image, timeWidget, const SizedBox(height: 8)],
        ));
  }

  void _onDelete(FlowerTimelineItem item) async {
    final dateFormat = DateFormat.MMMd(context.locale.languageCode);

    final desc = 'timeline.delete_alert'
        .tr(namedArgs: {"date": dateFormat.format(item.dateTime), "time": timeFormatter.format(item.dateTime)});
    final isDel = await showAlertDialog('alert'.tr(), desc, context);
    if (!(isDel ?? true)) {
      return;
    }

    final controller = ref.read(flowerTimelineControllerProvider(widget.flower).notifier);
    await controller.deleteRecord(item);
  }

  void _onEdit(FlowerTimelineItem item) async {
    // 准备编辑器所需的数据
    // 将 PhotoRecord 转换为 MediaItem
    final mediaItems = item.photos.map((p) => MediaItem.fromPhotoRecord(p)).toList().reversed.toList();
    final arg = FlowerTimelineEditorItem(
        text: item.text, mediaItems: mediaItems, dateTime: item.dateTime);

    final result = await Navigator.pushNamed(context, FlowerTimelineEditor.routeName, arguments: arg);
    if (result == null) {
      return;
    }

    // 直接使用返回的 FlowerTimelineEditorItem
    final FlowerTimelineEditorItem editorItem = result as FlowerTimelineEditorItem;

    LoadingDialog.show();

    // 直接使用 MediaItem 列表
    await FlowerTimelineController.updateRecordWithMediaItems(item, editorItem, widget.flower.id, editorItem.mediaItems);

    ref.invalidate(flowerTimelineControllerProvider(widget.flower));
    LoadingDialog.dismiss();
  }

  double _getTextHeight(String text) {
    final textPainter = TextPainter(text: TextSpan(text: text), textDirection: ui.TextDirection.ltr)
      ..layout(minWidth: 0, maxWidth: double.infinity);

    return textPainter.height;
  }

  @override
  void initState() {
    super.initState();
  }
}
