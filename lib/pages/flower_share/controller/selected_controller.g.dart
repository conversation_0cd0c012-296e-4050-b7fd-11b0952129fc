// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'selected_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$flowerShareSelectedPhotosControllerHash() =>
    r'3aba7f3531887459a2002f1e030c2e87f3b1b71c';

/// See also [FlowerShareSelectedPhotosController].
@ProviderFor(FlowerShareSelectedPhotosController)
final flowerShareSelectedPhotosControllerProvider = AutoDisposeNotifierProvider<
    FlowerShareSelectedPhotosController, List<PhotoRecord>>.internal(
  FlowerShareSelectedPhotosController.new,
  name: r'flowerShareSelectedPhotosControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$flowerShareSelectedPhotosControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FlowerShareSelectedPhotosController
    = AutoDisposeNotifier<List<PhotoRecord>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
