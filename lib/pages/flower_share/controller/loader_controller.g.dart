// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'loader_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$flowerShareLoaderControllerHash() =>
    r'87cc74df38d41bd7b7cb2308233214fef7035467';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$FlowerShareLoaderController
    extends BuildlessAutoDisposeAsyncNotifier<List<PhotoRecord>> {
  late final Flower flower;

  FutureOr<List<PhotoRecord>> build(
    Flower flower,
  );
}

/// See also [FlowerShareLoaderController].
@ProviderFor(FlowerShareLoaderController)
const flowerShareLoaderControllerProvider = FlowerShareLoaderControllerFamily();

/// See also [FlowerShareLoaderController].
class FlowerShareLoaderControllerFamily
    extends Family<AsyncValue<List<PhotoRecord>>> {
  /// See also [FlowerShareLoaderController].
  const FlowerShareLoaderControllerFamily();

  /// See also [FlowerShareLoaderController].
  FlowerShareLoaderControllerProvider call(
    Flower flower,
  ) {
    return FlowerShareLoaderControllerProvider(
      flower,
    );
  }

  @override
  FlowerShareLoaderControllerProvider getProviderOverride(
    covariant FlowerShareLoaderControllerProvider provider,
  ) {
    return call(
      provider.flower,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'flowerShareLoaderControllerProvider';
}

/// See also [FlowerShareLoaderController].
class FlowerShareLoaderControllerProvider
    extends AutoDisposeAsyncNotifierProviderImpl<FlowerShareLoaderController,
        List<PhotoRecord>> {
  /// See also [FlowerShareLoaderController].
  FlowerShareLoaderControllerProvider(
    Flower flower,
  ) : this._internal(
          () => FlowerShareLoaderController()..flower = flower,
          from: flowerShareLoaderControllerProvider,
          name: r'flowerShareLoaderControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$flowerShareLoaderControllerHash,
          dependencies: FlowerShareLoaderControllerFamily._dependencies,
          allTransitiveDependencies:
              FlowerShareLoaderControllerFamily._allTransitiveDependencies,
          flower: flower,
        );

  FlowerShareLoaderControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.flower,
  }) : super.internal();

  final Flower flower;

  @override
  FutureOr<List<PhotoRecord>> runNotifierBuild(
    covariant FlowerShareLoaderController notifier,
  ) {
    return notifier.build(
      flower,
    );
  }

  @override
  Override overrideWith(FlowerShareLoaderController Function() create) {
    return ProviderOverride(
      origin: this,
      override: FlowerShareLoaderControllerProvider._internal(
        () => create()..flower = flower,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        flower: flower,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<FlowerShareLoaderController,
      List<PhotoRecord>> createElement() {
    return _FlowerShareLoaderControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FlowerShareLoaderControllerProvider &&
        other.flower == flower;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, flower.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin FlowerShareLoaderControllerRef
    on AutoDisposeAsyncNotifierProviderRef<List<PhotoRecord>> {
  /// The parameter `flower` of this provider.
  Flower get flower;
}

class _FlowerShareLoaderControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<FlowerShareLoaderController,
        List<PhotoRecord>> with FlowerShareLoaderControllerRef {
  _FlowerShareLoaderControllerProviderElement(super.provider);

  @override
  Flower get flower => (origin as FlowerShareLoaderControllerProvider).flower;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
