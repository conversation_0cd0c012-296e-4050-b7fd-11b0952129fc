// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'archive_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchArchivesHash() => r'399e0aca1b909771d35389714ce690d4a8c3b069';

/// See also [fetchArchives].
@ProviderFor(fetchArchives)
final fetchArchivesProvider = AutoDisposeFutureProvider<List<Flower>>.internal(
  fetchArchives,
  name: r'fetchArchivesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchArchivesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FetchArchivesRef = AutoDisposeFutureProviderRef<List<Flower>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
