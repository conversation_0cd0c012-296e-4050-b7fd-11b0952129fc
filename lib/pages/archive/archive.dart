import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/resources.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/pages/archive/data/archive_repository.dart';
import 'package:flower_timemachine/pages/flower_detail/flower_detail.dart';
import 'package:flower_timemachine/widgets/ftm_box.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

final dateFormat = DateFormat.yMMMMd();
final timeFormat = DateFormat.Hm();

class Archive extends ConsumerStatefulWidget {
  static const routeName = "archive";

  const Archive({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _ArchiveState();
}

class _ArchiveState extends ConsumerState<Archive> {
  @override
  Widget build(BuildContext context) {
    var archivesAsync = ref.watch(fetchArchivesProvider);

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        title: const Text("archive").tr(),
        centerTitle: true,
      ),
      body: SafeArea(
          child: archivesAsync.when(
              data: (archives) => archives.isEmpty
                  ? Center(child: const Text("archive_page.empty_data").tr())
                  : ListView.builder(
                      itemBuilder: (context, index) => itemBuilder(context, index, archives),
                      itemCount: archives.length,
                    ),
              error: (error, stackTrace) => Text(error.toString()),
              loading: () => Container(
                  padding: const EdgeInsets.only(top: 8),
                  height: 40,
                  alignment: Alignment.center,
                  child: const SizedBox(height: 20, width: 20, child: CircularProgressIndicator())))),
    );
  }

  Widget? itemBuilder(BuildContext context, int index, List<Flower> archives) {
    if (index >= archives.length) {
      return null;
    }

    final flower = archives[index];
    if (flower.archiveTime == null) {
      return null;
    }

    Image avatar;
    if (flower.avatar != null) {
      avatar = Image.file(File(flower.avatar!), width: 66, height: 66, fit: BoxFit.cover);
    } else {
      avatar = Image.asset(R.iconFlower, width: 66, height: 66, fit: BoxFit.cover);
    }

    final dateTime = DateTime.fromMillisecondsSinceEpoch(flower.archiveTime! * 1000);
    final date = "${dateFormat.format(dateTime)} ${timeFormat.format(dateTime)}";

    // 获取归档原因（如果有）
    final String? archiveReason = flower.archiveReason;

    return FTMBox(
      circular: 10,
      child: ListTile(
        leading: ClipRRect(borderRadius: BorderRadius.circular(8.0), child: avatar),
        contentPadding: const EdgeInsets.only(left: 10, right: 5, top: 4, bottom: 4),
        title: Padding(padding: const EdgeInsets.only(bottom: 4.0), child: Text(flower.name)),
        isThreeLine: archiveReason != null && archiveReason.isNotEmpty,
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text("archive_page.archive_time".tr(namedArgs: {"date": date})),
            if (archiveReason != null && archiveReason.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 4.0),
                child: Text(
                  "archive_page.archive_reason".tr(namedArgs: {"reason": archiveReason}),
                  maxLines: 2,
                  overflow: TextOverflow.visible,
                ),
              ),
          ],
        ),
        trailing: IconButton(
          icon: const Icon(Icons.unarchive_outlined),
          onPressed: () => unarchive(flower),
        ),
        onTap: () => navigateToFlowerDetail(flower),
      ),
    );
  }

  void unarchive(Flower flower) async {
    await flower.unarchive();
    ref.invalidate(fetchArchivesProvider);
  }

  void navigateToFlowerDetail(Flower flower) async {
    final resultRef = FlowerDetailResultRef();
    await Navigator.pushNamed(
      context,
      FlowerDetailPage.name,
      arguments: [flower, resultRef],
    );

    if (resultRef.result == FlowerDetailResult.edited || resultRef.result == FlowerDetailResult.deleted) {
      ref.invalidate(fetchArchivesProvider);
    }
  }
}
