import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class RemindHelp extends StatefulWidget {
  const RemindHelp({super.key});

  static const String routeName = 'remind_help';
  @override
  State<RemindHelp> createState() => _RemindHelpState();
}

class _RemindHelpState extends State<RemindHelp> {
  late final WebViewController _controller;

  @override
  void initState() {
    super.initState();
    _controller = WebViewController();
    _controller.loadFlutterAsset('assets/remind_help.html');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('提醒说明'),
        elevation: 0,
        centerTitle: true,
      ),
      body: WebViewWidget(controller: _controller),
    );
  }
}