import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/models/network/app_version.dart';
import 'package:flower_timemachine/network/dio_client.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class Version {
  static late Version _instance;

  final PackageInfo packageInfo;

  Version._internal({required this.packageInfo});

  Future<VersionUpdate?> checkUpdate(BuildContext context) async {
    final versionArray = packageInfo.version.split('.');
    int versionCode = 0;
    int factor = 1;
    for (int i = versionArray.length - 1; i >= 0; i--) {
      versionCode += int.parse(versionArray[i]) * factor;
      factor *= 100;
    }

    int platform = -1;
    if (Platform.isAndroid) {
      platform = 2;
    } else if (Platform.isIOS) {
      platform = 1;
    }

    try {
      final rs = await DioClient.dio.get('$flowerHost/check_update',
          queryParameters: {'Version': packageInfo.version, 'Platform': platform, 'VersionCode': versionCode});

      final versionInfo = VersionUpdate.fromJson(rs.data);
      if (versionInfo.Version == null) {
        return null;
      }

      return versionInfo;
    } on DioException catch (e, s) {
      if (e.type == DioExceptionType.connectionError || e.type == DioExceptionType.connectionTimeout) {
        return null;
      }

      debugPrint("checkUpdate 异常 $e, $s");
      Sentry.captureException(e, stackTrace: s);
      return null;
    }
  }

  bool needPopupWindow(VersionUpdate versionInfo) {
    final checkInfo = ShareConfig.getVersionCheckInfo();
    if (checkInfo != null) {
      Map<String, dynamic> info = json.decode(checkInfo);
      int versionCode = info['version_code'] ?? 0;
      bool skip = info['skip'] ?? false;
      int? lastIgnoreTime = info['ignore_time'];

      if ((versionInfo.VersionCode ?? 0) <= versionCode) {
        // 忽略小于或等于当前版本
        if (skip) {
          return false;
        }

        // 3 天内不重复提醒
        if (lastIgnoreTime != null) {
          if (DateTime.now().difference(DateTime.fromMillisecondsSinceEpoch(lastIgnoreTime)).inDays < 3) {
            return false;
          }
        }
      }
    }

    return true;
  }

  static Future<void> init() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();

    _instance = Version._internal(packageInfo: packageInfo);
  }

  static Version get instance => _instance;
}
