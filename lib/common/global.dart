import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/controller/current_home_view_mode.dart';
import 'package:flower_timemachine/controller/user_controller.dart';
import 'package:flower_timemachine/types/flower_list_sort_type.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

import 'db_helper.dart';

class DB {
  static DB? _instance;
  final Database sqlite;

  DB._internal({required this.sqlite});

  static Future<DB> get() async {
    return _instance ??= DB._internal(
        sqlite: await openDatabase(await getDatabaseFilePath(),
            version: DBHelper.version, onCreate: DBHelper.onCreate, onUpgrade: DBHelper.onUpgrade));
  }

  static Future<void> close() async {
    await _instance?.sqlite.close();
    _instance = null;
  }

  static Future<String> getDatabaseFilePath() async => join(await getDatabasesPath(), 'database.db');
}

class ShareConfig {
  static late SharedPreferences _prefs;
  static int? _alarmHour;
  static int? _alarmMinute;
  static bool? _showCreateDay;
  static HomeViewMode? _homeViewMode;
  static bool? _showOnlySetNurtureTypes;
  static bool? _showAlbumTimeline;
  static bool? _showFutureNurture;
  static bool? _showHistoryNurture;
  static bool? _autoDetectPhotoDate;
  static const String _gardenTitleKey = 'garden_title';

  static Future<SharedPreferences> init() async {
    return _prefs = await SharedPreferences.getInstance();
  }

  static int getAlarmHour() {
    if (_alarmHour != null) {
      return _alarmHour!;
    }

    // 默认早点 9 点
    _alarmHour = _prefs.getInt('alarm_hour') ?? 9;
    return _alarmHour!;
  }

  static int getAlarmMinute() {
    if (_alarmMinute != null) {
      return _alarmMinute!;
    }

    _alarmMinute = _prefs.getInt('alarm_minute') ?? 0;
    return _alarmMinute!;
  }

  static int setAlarmHour(int value) {
    _alarmHour = value;

    _prefs.setInt('alarm_hour', value);
    return value;
  }

  static int setAlarmMinute(int value) {
    _alarmMinute = value;

    _prefs.setInt('alarm_minute', value);
    return value;
  }

  static int getAppInstallTime() {
    var installTime = _prefs.getInt('app_install_time');
    if (installTime == null) {
      installTime = DateTime.now().millisecondsSinceEpoch;
      _prefs.setInt('app_install_time', installTime);
    }

    return installTime;
  }

  static bool getIsShowAppScoreView() {
    return _prefs.getBool('is_show_app_score_view') ?? false;
  }

  static void setIsShowAppScoreView(bool value) {
    _prefs.setBool('is_show_app_score_view', value);
  }

  static bool getShowCreateDay() {
    return _showCreateDay ??= _prefs.getBool('is_show_create_day') ?? true;
  }

  static void setShowCreateDay(bool value) {
    _showCreateDay = value;
    _prefs.setBool('is_show_create_day', value);
  }

  static String? getVersionCheckInfo() {
    return _prefs.getString('version_check_info');
  }

  static void setVersionCheckInfo(String info) {
    _prefs.setString('version_check_info', info);
  }

  static String? getBackupState() {
    return _prefs.getString('backup_state');
  }

  static void setBackupState(String state) {
    _prefs.setString('backup_state', state);
  }

  static HomeViewMode getHomeViewMode() {
    return _homeViewMode ??= HomeViewMode.values[_prefs.getInt('home_view_mode') ?? 1];
  }

  static void setHomeViewMode(HomeViewMode value) {
    _homeViewMode = value;
    _prefs.setInt('home_view_mode', value.index);
  }

  static bool getShowOnlySetNurtureTypes() {
    return _showOnlySetNurtureTypes ??= _prefs.getBool('show_only_set_nurture_types') ?? false;
  }

  static void setShowOnlySetNurtureTypes(bool value) {
    _showOnlySetNurtureTypes = value;
    _prefs.setBool('show_only_set_nurture_types', value);
  }

  static String getGardenTitle() {
    return _prefs.getString(_gardenTitleKey) ?? 'garden'.tr();
  }

  static Future<bool> setGardenTitle(String title) {
    return _prefs.setString(_gardenTitleKey, title);
  }

  static bool getIsShowTagManagerShowCase() {
    return _prefs.getBool('is_show_tag_manager_show_case') ?? true;
  }

  static void setIsShowTagManagerShowCase(bool value) {
    _prefs.setBool('is_show_tag_manager_show_case', value);
  }

  static bool getShowAlbumTimeline() {
    return _showAlbumTimeline ??= _prefs.getBool('show_album_timeline') ?? false;
  }

  static void setShowAlbumTimeline(bool value) {
    _showAlbumTimeline = value;
    _prefs.setBool('show_album_timeline', value);
  }

  static FlowerListSortType? _flowerListSortType;

  static bool getShowFutureNurture() {
    return _showFutureNurture ??= _prefs.getBool('show_future_nurture') ?? true;
  }

  static void setShowFutureNurture(bool value) {
    _showFutureNurture = value;
    _prefs.setBool('show_future_nurture', value);
  }

  static bool getShowHistoryNurture() {
    return _showHistoryNurture ??= _prefs.getBool('show_history_nurture') ?? true;
  }

  static void setShowHistoryNurture(bool value) {
    _showHistoryNurture = value;
    _prefs.setBool('show_history_nurture', value);
  }

  static FlowerListSortType getFlowerListSortType() {
    return _flowerListSortType ??= FlowerListSortType
        .values[_prefs.getInt('flower_list_sort_type') ?? FlowerListSortType.nextMaintenanceAsc.index];
  }

  static void setFlowerListSortType(FlowerListSortType value) {
    _flowerListSortType = value;
    _prefs.setInt('flower_list_sort_type', value.index);
  }

  static bool getAutoDetectPhotoDate() {
    return _autoDetectPhotoDate ??= _prefs.getBool('auto_detect_photo_date') ?? UserController.get().isVip();
  }

  static void setAutoDetectPhotoDate(bool value) {
    _autoDetectPhotoDate = value;
    _prefs.setBool('auto_detect_photo_date', value);
  }

  static SharedPreferences get prefs => _prefs;
}

class Global {
  static late final String photoRecordDir;
  static late final String avatarDir;

  static Future<void> init() async {
    final baseDirPath = await getDataDir();

    debugPrint('baseDirPath: $baseDirPath');

    final photoRecordDirObj = await Directory('$baseDirPath/photo_records').create(recursive: true);
    photoRecordDir = photoRecordDirObj.path;

    final avatarDirObj = await Directory('$baseDirPath/avatar').create(recursive: true);
    avatarDir = avatarDirObj.path;
  }

  static Future<String> getDataDir() async {
    return (await getApplicationDocumentsDirectory()).path;
  }
}

const logKey = 'chenwuapp1234567';
const logIv = 'chenwuapp7654321';

const flowerTagMaxNumber = 3;

const flowerHost = 'https://flower.chenwuapp.com';
// const flowerHost = 'http://************:8080';