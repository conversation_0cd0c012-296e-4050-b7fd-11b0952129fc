import 'dart:io';
import 'dart:typed_data';

import 'package:encrypt/encrypt.dart';
import 'package:crypto/crypto.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:uuid/uuid.dart';

import 'global.dart';

Future<String> copyToPhotoRecordDir(String photoFile) async {
  final dir = Global.photoRecordDir;

  const uuid = Uuid();
  final fileName = uuid.v4();
  var suffix = 'jpg';
  final fileExtension = extension(photoFile);
  if (fileExtension.isNotEmpty) {
    suffix = fileExtension.substring(1);
  }

  await File(photoFile).copy('$dir/$fileName.$suffix');

  return '$fileName.$suffix';
}

Future<File> copyDataBases() async {
  final dbPath = join(await getDatabasesPath(), 'database.db');
  Directory tempDir = await getTemporaryDirectory();
  String tempFile = '${tempDir.path}/database.db.log';
  String logFile = '${tempDir.path}/log.bin';

  final newFile = await File(dbPath).copy(tempFile);

  final data = await newFile.readAsBytes();

  final key = Key.fromUtf8(logKey);
  final iv = IV.fromUtf8(logIv);
  final encrypter = Encrypter(AES(key, mode: AESMode.cbc));

  final encrypted = encrypter.encryptBytes(data, iv: iv);

  final log = await File(logFile).create();

  await log.writeAsBytes(encrypted.bytes, mode: FileMode.writeOnly, flush: true);

  await newFile.delete();

  return log;
}

Future<String> writeToAvatarRecordDir(Uint8List fileData) async {
  final dir = Global.avatarDir;

  await Directory(dir).create(recursive: true);

  final fileName = sha256.convert(fileData.toList()).toString();

  String tempDir = (await getTemporaryDirectory()).path;
  final tempFile = '$tempDir/$fileName';

  final tempAvatarFile = await File(tempFile).create(recursive: true);
  await tempAvatarFile.writeAsBytes(fileData, flush: true);

  var suffix = 'jpg';
  final fileExtension = extension(tempAvatarFile.path);
  if (fileExtension.isNotEmpty) {
    // 去掉开头的点号
    suffix = fileExtension.substring(1);
  }

  final filePath = '$dir/$fileName.$suffix';
  if (await File(filePath).exists()) {
    await tempAvatarFile.delete();
  } else {
    await tempAvatarFile.rename(filePath);
  }

  return '$fileName.$suffix';
}

Future<File> writeToTempFile(Uint8List fileData, String fileName) async {
  String tempDir = (await getTemporaryDirectory()).path;
  final tempFile = '$tempDir/$fileName';

  final tempAvatarFile = await File(tempFile).create(recursive: true);
  await tempAvatarFile.writeAsBytes(fileData, flush: true);

  return tempAvatarFile;
}
