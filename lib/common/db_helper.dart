import 'package:flower_timemachine/controller/maintenance_type.dart';
import 'package:flower_timemachine/models/flower_notes.dart';
import 'package:flower_timemachine/models/flower_nurture_cycle_mapping.dart';
import 'package:flower_timemachine/models/flower_monthly_nurture_cycle.dart';
import 'package:flower_timemachine/models/flower_timeline.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/models/tag_info.dart';
import 'package:flower_timemachine/models/tag_info_mapping.dart';
import 'package:flower_timemachine/models/transaction.dart' as transaction_model;
import 'package:flutter/foundation.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:sqflite/sqflite.dart';
import 'package:r_string_transform/r_string_transform.dart';

import '../models/flower.dart';
import '../models/maintenance_record.dart';
import '../models/next_task_time.dart';
import '../models/notification_model.dart';
import '../models/photo_record.dart';

class DBHelper {
  static const version = 15;

  static Future<void> onCreate(Database db, int version) async {
    await db.execute(Flower.createTable());
    await db.execute(MaintenanceRecord.createTable());
    await db.execute(PhotoRecord.createTable());
    await db.execute(NextMaintenanceTime.createTable());
    await db.execute(NotificationModel.createTable());
    await db.execute(TagInfo.createTable());
    await db.execute(TagInfoMapping.createTable());
    await db.execute(FlowerTimeline.createTable());
    await db.execute(transaction_model.Transaction.createTable());
    await db.execute(NurtureType.createTable());
    await NurtureType.createDefaultTypes(db);
    await db.execute(FlowerNurtureCycleMapping.createTable());
    await db.execute(FlowerMonthlyNurtureCycle.createTable());
    await db.execute(FlowerNotes.createTable());
  }

  static Future<void> onUpgrade(Database db, int oldVersion, int newVersion) async {
    debugPrint('onUpgrade from $oldVersion to $newVersion');
    if (oldVersion == 1) {
      _onUpgradeTo2(db, oldVersion, newVersion);
      oldVersion = 2;
    }
    if (oldVersion == 2) {
      await _onUpgradeTo3(db, oldVersion, newVersion);
      oldVersion = 3;
    }
    if (oldVersion == 3) {
      await _onUpgradeTo4(db, oldVersion, newVersion);
      oldVersion = 4;
    }
    if (oldVersion == 4) {
      await _onUpgradeTo5(db, oldVersion, newVersion);
      oldVersion = 5;
    }
    if (oldVersion == 5) {
      await _onUpgradeTo6(db, oldVersion, newVersion);
      oldVersion = 6;
    }
    if (oldVersion == 6) {
      await _onUpgradeTo7(db, oldVersion, newVersion);
      oldVersion = 7;
    }
    if (oldVersion == 7) {
      await _onUpgradeTo8(db, oldVersion, newVersion);
      oldVersion = 8;
    }
    if (oldVersion == 8) {
      await _onUpgradeTo9(db, oldVersion, newVersion);
      oldVersion = 9;
    }
    if (oldVersion == 9) {
      await _onUpgradeTo10(db, oldVersion, newVersion);
      oldVersion = 10;
    }
    if (oldVersion == 10) {
      await _onUpgradeTo11(db, oldVersion, newVersion);
      oldVersion = 11;
    }
    if (oldVersion == 11) {
      await _onUpgradeTo12(db, oldVersion, newVersion);
      oldVersion = 12;
    }
    if (oldVersion == 12) {
      await _onUpgradeTo13(db, oldVersion, newVersion);
      oldVersion = 13;
    }
    if (oldVersion == 13) {
      await _onUpgradeTo14(db, oldVersion, newVersion);
      oldVersion = 14;
    }
    if (oldVersion == 14) {
      await _onUpgradeTo15(db, oldVersion, newVersion);
      oldVersion = 15;
    }
  }

  static Future<void> _onUpgradeTo2(Database db, int oldVersion, int newVersion) async {
    await db.execute(TagInfo.createTable());
    await db.execute(TagInfoMapping.createTable());
  }

  static Future<void> _onUpgradeTo3(Database db, int oldVersion, int newVersion) async {
    await db.execute(FlowerTimeline.createTable());

    // 根据 photo_record 表，建立 flower_timeline 表

    final List<Map<String, dynamic>> result = await db.rawQuery('SELECT * FROM photo_records');
    final Map<int, Map<int, List<int>>> newRecords = {};

    // 将 photo_record 的数据，按 flowerId 跟 time(同一天）分组
    for (final item in result) {
      int flowerId = item['flowerId'];
      final date = newRecords.putIfAbsent(flowerId, () => {});

      DateTime currentTs = DateTime.fromMillisecondsSinceEpoch((item['time'] as int) * 1000);
      currentTs = DateTime(currentTs.year, currentTs.month, currentTs.day);
      final currentDate = currentTs.millisecondsSinceEpoch;

      final photos = date.putIfAbsent(currentDate, () => []);
      photos.add(item['id']);
    }

    // 每组建立一条 flower_timeline 的记录
    Map<int, int> updatePhotoRecordMap = {};
    for (final flowerId in newRecords.keys) {
      for (final timestamp in newRecords[flowerId]!.keys) {
        final id = await db.insert(FlowerTimeline.tableName, {'flowerId': flowerId, 'time': timestamp / 1000});

        for (final photoRecordId in newRecords[flowerId]![timestamp]!) {
          updatePhotoRecordMap[photoRecordId] = id;
        }
      }
    }

    await db.execute('ALTER TABLE photo_records ADD flowerTimelineId int');

    // photo_records 的 flowerTimelineId 对应 flower_timeline 组
    final batch = db.batch();
    for (final photoRecordId in updatePhotoRecordMap.keys) {
      batch.update('photo_records', {'flowerTimelineId': updatePhotoRecordMap[photoRecordId]!},
          where: 'id = ?', whereArgs: [photoRecordId]);
    }

    await batch.commit();
  }

  static Future<void> _onUpgradeTo4(Database db, int oldVersion, int newVersion) async {
    await db.execute('ALTER TABLE ${FlowerTimeline.tableName} RENAME TO ${FlowerTimeline.tableName}_del');
    await db.execute(FlowerTimeline.createTable());

    await db.execute('INSERT INTO ${FlowerTimeline.tableName}(id, flowerId, text, time) '
        'SELECT id, flowerId, text, time FROM ${FlowerTimeline.tableName}_del');
  }

  static Future<void> _onUpgradeTo5(Database db, int oldVersion, int newVersion) async {
    await db.execute(transaction_model.Transaction.createTable());
  }

  static Future<void> _onUpgradeTo6(Database db, int oldVersion, int newVersion) async {
    await db.execute(NurtureType.createTable());
    await NurtureType.createDefaultTypes(db);

    await db.execute(FlowerNurtureCycleMapping.createTable());

    final List<Map<String, dynamic>> result = await db.rawQuery('SELECT * FROM ${Flower.tableName} ');

    final batch = db.batch();
    for (final row in result) {
      int flowerId = row["id"];
      int? wateringTime = row["wateringTime"];
      int? fertilizeTime = row["fertilizeTime"];
      int? pruningTime = row["pruningTime"];
      int? pestControlTime = row["pestControlTime"];

      if (wateringTime != null) {
        batch.insert(FlowerNurtureCycleMapping.tableName,
            {"flower": flowerId, "type": MaintenanceType.watering.index, "cycle": wateringTime},
            conflictAlgorithm: ConflictAlgorithm.replace);
      }
      if (fertilizeTime != null) {
        batch.insert(FlowerNurtureCycleMapping.tableName,
            {"flower": flowerId, "type": MaintenanceType.fertilize.index, "cycle": fertilizeTime},
            conflictAlgorithm: ConflictAlgorithm.replace);
      }
      if (pruningTime != null) {
        batch.insert(FlowerNurtureCycleMapping.tableName,
            {"flower": flowerId, "type": MaintenanceType.pruning.index, "cycle": pruningTime},
            conflictAlgorithm: ConflictAlgorithm.replace);
      }
      if (pestControlTime != null) {
        batch.insert(FlowerNurtureCycleMapping.tableName,
            {"flower": flowerId, "type": MaintenanceType.pestControl.index, "cycle": pestControlTime},
            conflictAlgorithm: ConflictAlgorithm.replace);
      }
    }

    batch.commit();
  }

  static Future<void> _onUpgradeTo7(Database db, int oldVersion, int newVersion) async {
    await db.execute('ALTER TABLE ${Flower.tableName} ADD arrivalTime int');
  }

  static Future<void> _onUpgradeTo8(Database db, int oldVersion, int newVersion) async {
    await db.execute('ALTER TABLE ${Flower.tableName} ADD archiveTime int');
  }

  static Future<void> _onUpgradeTo9(Database db, int oldVersion, int newVersion) async {
    await db.execute(FlowerNotes.createTable());
  }

  static Future<void> _onUpgradeTo10(Database db, int oldVersion, int newVersion) async {
    await db.execute('ALTER TABLE ${MaintenanceRecord.tableName} ADD remark TEXT');
  }

  static Future<void> _onUpgradeTo11(Database db, int oldVersion, int newVersion) async {
    try {
      await db.execute('ALTER TABLE ${TagInfo.tableName} ADD sort_order INTEGER');
      await db.execute('UPDATE ${TagInfo.tableName} SET sort_order = id');
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  static Future<void> _onUpgradeTo12(Database db, int oldVersion, int newVersion) async {
    final stringTransform = RStringTransform();
    await db.execute('ALTER TABLE ${Flower.tableName} ADD normalizedName TEXT');
    try {
      final flowers = await db.query(Flower.tableName);
      final batch = db.batch();
      for (final flower in flowers) {
        final name = flower['name'] as String;
        if (name.isEmpty) {
          continue;
        }

        final normalizedName = await stringTransform.transformString(name);
        if (normalizedName?.isEmpty ?? true) {
          continue;
        }
        batch.update(Flower.tableName, {'normalizedName': normalizedName}, where: 'id = ?', whereArgs: [flower['id']]);
      }
      await batch.commit();
    } catch (e, stackTrace) {
      Sentry.captureException(e, stackTrace: stackTrace);
      debugPrint(e.toString());
    }
  }

  static Future<void> _onUpgradeTo13(Database db, int oldVersion, int newVersion) async {
    try {
      // 为花卉表添加归档原因字段
      await db.execute('ALTER TABLE ${Flower.tableName} ADD ${Flower.archiveReasonField} TEXT');
      debugPrint('成功添加归档原因字段');
    } catch (e, stackTrace) {
      Sentry.captureException(e, stackTrace: stackTrace);
      debugPrint('添加归档原因字段失败: ${e.toString()}');
    }
  }

  static Future<void> _onUpgradeTo14(Database db, int oldVersion, int newVersion) async {
    try {
      // 为照片记录表添加媒体类型字段
      await db.execute('ALTER TABLE ${PhotoRecord.tableName} ADD ${PhotoRecord.mediaTypeField} INTEGER DEFAULT 0');
      await db.execute('ALTER TABLE ${PhotoRecord.tableName} ADD ${PhotoRecord.thumbnailFileField} TEXT');
    } catch (e, stackTrace) {
      Sentry.captureException(e, stackTrace: stackTrace);
    }
  }

  static Future<void> _onUpgradeTo15(Database db, int oldVersion, int newVersion) async {
    try {
      // 创建新的月份养护周期表
      await db.execute(FlowerMonthlyNurtureCycle.createTable());
      debugPrint('成功创建月份养护周期表');

      // 从旧的养护周期表迁移数据
      await _migrateNurtureCycleData(db);
      debugPrint('成功迁移养护周期数据');
    } catch (e, stackTrace) {
      Sentry.captureException(e, stackTrace: stackTrace);
      debugPrint('创建月份养护周期表失败: ${e.toString()}');
    }
  }

  static Future<void> _migrateNurtureCycleData(Database db) async {
    try {
      // 查询所有旧的养护周期数据
      final List<Map<String, dynamic>> oldCycles = await db.query(
        FlowerNurtureCycleMapping.tableName,
      );

      // 迁移数据
      final batch = db.batch();
      for (final oldCycle in oldCycles) {
        final flowerId = oldCycle['flower'];
        final typeId = oldCycle['type'];
        final oldCycleValue = oldCycle['cycle'] as int;

        // 旧数据中 0 表示未设置，转换为新数据中的 -1
        final newCycleValue = oldCycleValue == 0 ? -1 : oldCycleValue;

        // 只为1月创建记录（使用迁移的数据），其他继承月份不写入数据库
        batch.insert(FlowerMonthlyNurtureCycle.tableName, {
          'flower_id': flowerId,
          'type_id': typeId,
          'month': 1,
          'cycle': newCycleValue,
        });
      }

      await batch.commit(noResult: true);
      debugPrint('成功迁移 ${oldCycles.length} 条养护周期数据');
    } catch (e, stackTrace) {
      Sentry.captureException(e, stackTrace: stackTrace);
      debugPrint('迁移养护周期数据失败: ${e.toString()}');
      rethrow;
    }
  }
}
