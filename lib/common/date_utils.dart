import 'package:easy_localization/easy_localization.dart';

String calcDatePrompt(DateTime date, DateTime now) {
  // 时间改为 0 点，否则不够 24 小时会当作当天
  final dateZeroClock = DateTime(date.year, date.month, date.day);
  final nowZeroClock = DateTime(now.year, now.month, now.day);
  final diff = nowZeroClock.difference(dateZeroClock);
  final inDay = diff.inDays;

  if (inDay > 365) {
    var diffYear = (inDay / 365).truncate();
    return "nurture_reminder.years_hint".tr(namedArgs: {"year": diffYear.toString()});
  } else if (inDay > 30) {
    var diffMonth = (inDay / 30).truncate();
    return "nurture_reminder.month_hint".tr(namedArgs: {"month": diffMonth.toString()});
  } else if (inDay > 1) {
    return "nurture_reminder.day_hint".tr(namedArgs: {"day": inDay.toString()});
  } else if (inDay == 1) {
    return "yesterday".tr();
  } else if (inDay >= 0) {
    return "today".tr();
  } else {
    return "";
  }
}


DateTime getTodayZeroClock() {
  final now = DateTime.now();
  return DateTime(now.year, now.month, now.day);
}