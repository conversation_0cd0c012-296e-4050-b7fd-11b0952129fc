import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/ftm_notification.dart';
import 'package:flower_timemachine/common/version.dart';
import 'package:flower_timemachine/controller/nurture_types_controller.dart';
import 'package:flower_timemachine/controller/user_controller.dart';
import 'package:flower_timemachine/models/notification_model.dart';
import 'package:flower_timemachine/pages/language/controller/language_controller.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import 'common/global.dart';
import 'mainApp.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await EasyLocalization.ensureInitialized();

  await FTMNotification.init();
  await ShareConfig.init();
  await Global.init();
  await NotificationModel.clearOldRecord();
  await Version.init();
  await UserController().initUser();
  await NurtureTypesController.get().init();


  // 显示启动页
  await Future.delayed(const Duration(milliseconds: 500));

  Widget app = ProviderScope(
    child: EasyLocalization(
        supportedLocales: LanguageController.supportedLocales(),
        path: 'assets/translations',
        fallbackLocale: const Locale('en'),
        child: const MainAPP()
    )
  );

  if (kDebugMode) {
    return runApp(app);
  } else {
    await SentryFlutter.init(
            (options) {
          options.dsn = 'https://<EMAIL>/4505538750054400';
          options.tracesSampleRate = 1.0;
          options.attachScreenshot = true;
        },
        appRunner: () => runApp(app)
    );
  }
}
