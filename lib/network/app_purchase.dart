import 'dart:io';

import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/models/transaction.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'dio_client.dart';

part 'app_purchase.g.dart';
part 'app_purchase.freezed.dart';

@freezed
sealed class AppPurchaseResp with _$AppPurchaseResp {
  const factory AppPurchaseResp({
    // ignore: invalid_annotation_target
    @<PERSON>son<PERSON>ey(name: "IsVerify") required bool isVerify
  }) = _AppPurchaseResp;

  factory AppPurchaseResp.fromJson(Map<String, dynamic> json) => _$AppPurchaseRespFromJson(json);
}

@freezed
sealed class CheckVipResp with _$CheckVipResp {
  const factory CheckVipResp({
    // ignore: invalid_annotation_target
    @JsonKey(name: "IsVip") required bool isVip,
    // ignore: invalid_annotation_target
    @<PERSON>son<PERSON><PERSON>(name: "Refund") @Default(false) bool refund,
    // ignore: invalid_annotation_target
    @JsonKey(name: "FailedVerify") @Default(false) bool failedVerify
  }) = _CheckVipResp;

  factory CheckVipResp.fromJson(Map<String, dynamic> json) => _$CheckVipRespFromJson(json);
}

class AppPurchase {
  static Future<AppPurchaseResp> verify(String userId, String? productId, String serverVerificationData, DateTime createTime) async {
    int platform = -1;
    if (Platform.isAndroid) {
      platform = 2;
    } else if (Platform.isIOS) {
      platform = 1;
    }

    final rs = await DioClient.dio.post('$flowerHost/verify_purchase', data: {
      'UserId': userId,
      'PurchaseID': productId,
      'ServerVerificationData': serverVerificationData,
      'Platform': platform,
      'CreateTime': createTime.toUtc().toIso8601String()
    });

    return AppPurchaseResp.fromJson(rs.data);
  }

  static Future<CheckVipResp> checkVipValidity(bool isVip, String userId, Iterable<Transaction> transactions) async {
    int platform = -1;
    if (Platform.isAndroid) {
      platform = 2;
    } else if (Platform.isIOS) {
      platform = 1;
    }

    List<Map<String, dynamic>> transactionsParams = [];
    for (final item in transactions) {
      transactionsParams.add({
        "PurchaseID": item.purchaseID,
        "ServerVerificationData": item.serverVerificationData,
        "Platform": platform,
        "CreateTime": DateTime.fromMillisecondsSinceEpoch(item.createTime).toUtc().toIso8601String(),
      });
    }

    final rs = await DioClient.dio.post('$flowerHost/check_vip', data: {
      'UserId': userId,
      'IsVip': isVip,
      'Purchase': transactionsParams,
    });

    return CheckVipResp.fromJson(rs.data);
  }
}