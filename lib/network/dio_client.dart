import 'package:dio/dio.dart';
import 'encryption_transformer.dart';

class DioClient {
  static Dio? _dio;

  static Dio get dio {
    if (_dio != null) {
      return _dio!;
    }

    final d = Dio(BaseOptions(
        connectTimeout: const Duration(seconds: 60), receiveTimeout: const Duration(seconds: 60))
    );
    d.transformer = BackgroundTransformer()..jsonDecodeCallback = EncryptionTransformer.parseJson;

    // d.httpClientAdapter = IOHttpClientAdapter()..onHttpClientCreate = (client) {
    //   // Config the client.
    //   client.findProxy = (uri) {
    //     // Forward all request to proxy "localhost:8888".
    //     return 'PROXY *************:8888';
    //   };
    //   // You can also create a new HttpClient for <PERSON><PERSON> instead of returning,
    //   // but a client must being returned here.
    //   return client;
    // };

    _dio = d;

    return d;
  }
}