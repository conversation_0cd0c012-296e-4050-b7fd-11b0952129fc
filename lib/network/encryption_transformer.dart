

import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:flower_timemachine/common/global.dart';


class EncryptionTransformer{
  static Key? _key;


  static Map<String, dynamic> parseJson(String response) {
    final key = getKey();
    final iv = IV.fromUtf8(logIv);

    final encryptor = Encrypter(AES(key, mode: AESMode.cbc));
    final decrypted = encryptor.decrypt64(response, iv: iv);

    return jsonDecode(decrypted) as Map<String, dynamic>;
  }


  static Key getKey() {
    if (_key != null) {
      return _key!;
    }

    // "123456com.chenwuapp.com"
    List<int> keyArray = [0xb8, 0xbb, 0xba, 0xbd, 0xbc, 0xbf, 0xea, 0xe6, 0xe4, 0xa7, 0xea, 0xe1, 0xec, 0xe7, 0xfe, 0xfc, 0xe8, 0xf9, 0xf9, 0xa7, 0xea, 0xe6, 0xe4];
    List<int> decodeKeyArray = [];
    for (final k in keyArray) {
      decodeKeyArray.add(k ^ 0x89);
    }

    final key = md5.convert(decodeKeyArray).toString();

    return _key = Key.fromUtf8(key);
  }
}