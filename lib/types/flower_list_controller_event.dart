import 'package:freezed_annotation/freezed_annotation.dart';

import '../models/flower.dart';

part 'flower_list_controller_event.freezed.dart';

@freezed
abstract class FlowerListEventType with _$FlowerListEventType {
  const factory FlowerListEventType.addFlower(Flower flower) = _AddFlower;
  const factory FlowerListEventType.deleteFlower(Flower flower) = _DeleteFlower;
  const factory FlowerListEventType.moveFlower(Flower flower) = _MoveFlower;
}
