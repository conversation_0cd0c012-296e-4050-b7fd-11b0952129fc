import 'package:flower_timemachine/types/monthly_cycle_data.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/controller/nurture_types_controller.dart';

class FlowerMonthlyCycles {
  Map<int, MonthlyCycleData> typesCycles = {}; // 养护类型ID -> 月份周期数据

  FlowerMonthlyCycles();

  // 获取某个养护类型的月份周期数据
  MonthlyCycleData? getCycleDataForType(NurtureType nurtureType) {
    return typesCycles[nurtureType.id];
  }

  // 设置某个养护类型的月份周期数据
  void setCycleDataForType(NurtureType nurtureType, MonthlyCycleData cycleData) {
    typesCycles[nurtureType.id] = cycleData;
  }

  // 获取某个养护类型某月的周期
  int getCycleForTypeAndMonth(NurtureType nurtureType, int month) {
    final cycleData = typesCycles[nurtureType.id];
    if (cycleData == null) {
      return 0; // 继承
    }
    return cycleData.getCycleForMonth(month);
  }

  // 设置某个养护类型某月的周期
  void setCycleForTypeAndMonth(NurtureType nurtureType, int month, int cycle) {
    final currentCycleData = typesCycles[nurtureType.id] ?? MonthlyCycleData.createDefault();
    currentCycleData.setCycleForMonth(month, cycle);
    typesCycles[nurtureType.id] = currentCycleData;
  }

  // 获取某个养护类型某月的实际周期（处理继承逻辑）
  int getEffectiveCycleForTypeAndMonth(NurtureType nurtureType, int month) {
    final cycleData = typesCycles[nurtureType.id];
    if (cycleData == null) {
      return nurtureType.defaultCycle;
    }
    return cycleData.getEffectiveCycleForMonth(month, nurtureType.defaultCycle);
  }

  // 兼容性方法：通过typeId获取某个养护类型某月的实际周期（从NurtureTypesController获取默认周期）
  int getEffectiveCycleForTypeAndMonthFromController(int typeId, int month) {
    final nurtureType = NurtureTypesController.get().getTypeInfo(typeId);
    if (nurtureType == null) {
      return 0; // 如果找不到养护类型，返回0表示无效
    }
    return getEffectiveCycleForTypeAndMonth(nurtureType, month);
  }

  // 获取某个养护类型某月的实际周期（简化版本，自动从控制器获取默认周期）
  int getEffectiveCycle(int typeId, int month) {
    return getEffectiveCycleForTypeAndMonthFromController(typeId, month);
  }

  // 兼容性方法：通过typeId设置某个养护类型某月的周期
  void setCycleForTypeAndMonthById(int typeId, int month, int cycle) {
    final currentCycleData = typesCycles[typeId] ?? MonthlyCycleData.createDefault();
    currentCycleData.setCycleForMonth(month, cycle);
    typesCycles[typeId] = currentCycleData;
  }

  // 移除某个养护类型的数据
  void removeType(NurtureType nurtureType) {
    typesCycles.remove(nurtureType.id);
  }

  // 创建空的花卉月份周期数据
  static FlowerMonthlyCycles createEmpty() {
    return FlowerMonthlyCycles();
  }

  // 复制对象
  FlowerMonthlyCycles copy() {
    final newFlowerCycles = FlowerMonthlyCycles();
    for (final entry in typesCycles.entries) {
      newFlowerCycles.typesCycles[entry.key] = entry.value.copy();
    }
    return newFlowerCycles;
  }

  // 从旧的养护周期数据创建（迁移用）
  static FlowerMonthlyCycles fromLegacyCycles(Map<int, int> legacyCycles) {
    final flowerCycles = FlowerMonthlyCycles();

    for (final entry in legacyCycles.entries) {
      final typeId = entry.key;
      final cycle = entry.value;

      // 旧数据中 0 表示未设置，转换为新数据中的 -1
      final newCycle = cycle == 0 ? -1 : cycle;
      flowerCycles.typesCycles[typeId] = MonthlyCycleData.fromSingleCycle(newCycle);
    }

    return flowerCycles;
  }
}
