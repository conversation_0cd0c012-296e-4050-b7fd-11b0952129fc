// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_purchase_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AppPurchaseState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() success,
    required TResult Function() purchased,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? success,
    TResult? Function()? purchased,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? success,
    TResult Function()? purchased,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AppPurchaseStateSuccess value) success,
    required TResult Function(_AppPurchaseStateNoProduct value) purchased,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AppPurchaseStateSuccess value)? success,
    TResult? Function(_AppPurchaseStateNoProduct value)? purchased,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AppPurchaseStateSuccess value)? success,
    TResult Function(_AppPurchaseStateNoProduct value)? purchased,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppPurchaseStateCopyWith<$Res> {
  factory $AppPurchaseStateCopyWith(
          AppPurchaseState value, $Res Function(AppPurchaseState) then) =
      _$AppPurchaseStateCopyWithImpl<$Res, AppPurchaseState>;
}

/// @nodoc
class _$AppPurchaseStateCopyWithImpl<$Res, $Val extends AppPurchaseState>
    implements $AppPurchaseStateCopyWith<$Res> {
  _$AppPurchaseStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppPurchaseState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$AppPurchaseStateSuccessImplCopyWith<$Res> {
  factory _$$AppPurchaseStateSuccessImplCopyWith(
          _$AppPurchaseStateSuccessImpl value,
          $Res Function(_$AppPurchaseStateSuccessImpl) then) =
      __$$AppPurchaseStateSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$AppPurchaseStateSuccessImplCopyWithImpl<$Res>
    extends _$AppPurchaseStateCopyWithImpl<$Res, _$AppPurchaseStateSuccessImpl>
    implements _$$AppPurchaseStateSuccessImplCopyWith<$Res> {
  __$$AppPurchaseStateSuccessImplCopyWithImpl(
      _$AppPurchaseStateSuccessImpl _value,
      $Res Function(_$AppPurchaseStateSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppPurchaseState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$AppPurchaseStateSuccessImpl implements _AppPurchaseStateSuccess {
  const _$AppPurchaseStateSuccessImpl();

  @override
  String toString() {
    return 'AppPurchaseState.success()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppPurchaseStateSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() success,
    required TResult Function() purchased,
  }) {
    return success();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? success,
    TResult? Function()? purchased,
  }) {
    return success?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? success,
    TResult Function()? purchased,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AppPurchaseStateSuccess value) success,
    required TResult Function(_AppPurchaseStateNoProduct value) purchased,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AppPurchaseStateSuccess value)? success,
    TResult? Function(_AppPurchaseStateNoProduct value)? purchased,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AppPurchaseStateSuccess value)? success,
    TResult Function(_AppPurchaseStateNoProduct value)? purchased,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class _AppPurchaseStateSuccess implements AppPurchaseState {
  const factory _AppPurchaseStateSuccess() = _$AppPurchaseStateSuccessImpl;
}

/// @nodoc
abstract class _$$AppPurchaseStateNoProductImplCopyWith<$Res> {
  factory _$$AppPurchaseStateNoProductImplCopyWith(
          _$AppPurchaseStateNoProductImpl value,
          $Res Function(_$AppPurchaseStateNoProductImpl) then) =
      __$$AppPurchaseStateNoProductImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$AppPurchaseStateNoProductImplCopyWithImpl<$Res>
    extends _$AppPurchaseStateCopyWithImpl<$Res,
        _$AppPurchaseStateNoProductImpl>
    implements _$$AppPurchaseStateNoProductImplCopyWith<$Res> {
  __$$AppPurchaseStateNoProductImplCopyWithImpl(
      _$AppPurchaseStateNoProductImpl _value,
      $Res Function(_$AppPurchaseStateNoProductImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppPurchaseState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$AppPurchaseStateNoProductImpl implements _AppPurchaseStateNoProduct {
  const _$AppPurchaseStateNoProductImpl();

  @override
  String toString() {
    return 'AppPurchaseState.purchased()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppPurchaseStateNoProductImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() success,
    required TResult Function() purchased,
  }) {
    return purchased();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? success,
    TResult? Function()? purchased,
  }) {
    return purchased?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? success,
    TResult Function()? purchased,
    required TResult orElse(),
  }) {
    if (purchased != null) {
      return purchased();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AppPurchaseStateSuccess value) success,
    required TResult Function(_AppPurchaseStateNoProduct value) purchased,
  }) {
    return purchased(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AppPurchaseStateSuccess value)? success,
    TResult? Function(_AppPurchaseStateNoProduct value)? purchased,
  }) {
    return purchased?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AppPurchaseStateSuccess value)? success,
    TResult Function(_AppPurchaseStateNoProduct value)? purchased,
    required TResult orElse(),
  }) {
    if (purchased != null) {
      return purchased(this);
    }
    return orElse();
  }
}

abstract class _AppPurchaseStateNoProduct implements AppPurchaseState {
  const factory _AppPurchaseStateNoProduct() = _$AppPurchaseStateNoProductImpl;
}
