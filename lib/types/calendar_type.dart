import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/models/flower_timeline.dart';
import 'package:flower_timemachine/models/maintenance_record.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'calendar_type.freezed.dart';

enum CalendarType {
  nurture,
  timeline,
}

@freezed
class CalendarState with _$CalendarState {
  const factory CalendarState({
    required DateTime time,
    required CalendarType type,
    required FlowerTimeline? timeline,
    required List<String>? photos,
    required MaintenanceRecord? nurture,
    required Flower flower,
  }) = _CalendarState;
}
