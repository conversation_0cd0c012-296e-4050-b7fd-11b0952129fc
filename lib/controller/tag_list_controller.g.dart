// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tag_list_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$tagListControllerHash() => r'29a8a1f1143bc00aa64f78e6f762dff67e336f64';

/// See also [TagListController].
@ProviderFor(TagListController)
final tagListControllerProvider =
    AutoDisposeAsyncNotifierProvider<TagListController, List<TagInfo>>.internal(
  TagListController.new,
  name: r'tagListControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$tagListControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TagListController = AutoDisposeAsyncNotifier<List<TagInfo>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
