import 'package:flower_timemachine/models/nurture_types.dart';

class NurtureTypesController {
  static NurtureTypesController? _instance;

  final List<NurtureType> _typesList = [];
  final Map<int, NurtureType> _typesMap = {};

  NurtureTypesController._();

  static NurtureTypesController get() {
    return _instance ??= NurtureTypesController._();
  }

  Future<void> init() async {
    _typesList.clear();
    _typesMap.clear();

    final typeList = await NurtureType.getAll(includeDisable: true);
    for (final typeInfo in typeList) {
      _typesList.add(typeInfo);
      _typesMap[typeInfo.id] = typeInfo;
    }
  }

  NurtureType? getTypeInfo(int type) {
    return _typesMap[type];
  }

  void add(NurtureType type) {
    _typesList.add(type);
    _typesMap[type.id] = type;
  }

  void del(NurtureType type) {
    _typesList.remove(type);
    _typesMap.remove(type.id);
  }

  List<NurtureType> get types => List.unmodifiable(_typesList);
  List<NurtureType> get enableTypes => List.unmodifiable(_typesList.where((element) => element.enable));
  List<NurtureType> get disabledTypes => List.unmodifiable(_typesList.where((element) => !element.enable));
}
