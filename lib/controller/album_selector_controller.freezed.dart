// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'album_selector_controller.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AlbumFilterData {
  int get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  AlbumFilteDataType get type => throw _privateConstructorUsedError;

  /// Create a copy of AlbumFilterData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AlbumFilterDataCopyWith<AlbumFilterData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AlbumFilterDataCopyWith<$Res> {
  factory $AlbumFilterDataCopyWith(
          AlbumFilterData value, $Res Function(AlbumFilterData) then) =
      _$AlbumFilterDataCopyWithImpl<$Res, AlbumFilterData>;
  @useResult
  $Res call({int id, String name, AlbumFilteDataType type});
}

/// @nodoc
class _$AlbumFilterDataCopyWithImpl<$Res, $Val extends AlbumFilterData>
    implements $AlbumFilterDataCopyWith<$Res> {
  _$AlbumFilterDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AlbumFilterData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as AlbumFilteDataType,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AlbumFilterDataImplCopyWith<$Res>
    implements $AlbumFilterDataCopyWith<$Res> {
  factory _$$AlbumFilterDataImplCopyWith(_$AlbumFilterDataImpl value,
          $Res Function(_$AlbumFilterDataImpl) then) =
      __$$AlbumFilterDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int id, String name, AlbumFilteDataType type});
}

/// @nodoc
class __$$AlbumFilterDataImplCopyWithImpl<$Res>
    extends _$AlbumFilterDataCopyWithImpl<$Res, _$AlbumFilterDataImpl>
    implements _$$AlbumFilterDataImplCopyWith<$Res> {
  __$$AlbumFilterDataImplCopyWithImpl(
      _$AlbumFilterDataImpl _value, $Res Function(_$AlbumFilterDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of AlbumFilterData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
  }) {
    return _then(_$AlbumFilterDataImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as AlbumFilteDataType,
    ));
  }
}

/// @nodoc

class _$AlbumFilterDataImpl implements _AlbumFilterData {
  const _$AlbumFilterDataImpl(
      {required this.id, required this.name, required this.type});

  @override
  final int id;
  @override
  final String name;
  @override
  final AlbumFilteDataType type;

  @override
  String toString() {
    return 'AlbumFilterData(id: $id, name: $name, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AlbumFilterDataImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, name, type);

  /// Create a copy of AlbumFilterData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AlbumFilterDataImplCopyWith<_$AlbumFilterDataImpl> get copyWith =>
      __$$AlbumFilterDataImplCopyWithImpl<_$AlbumFilterDataImpl>(
          this, _$identity);
}

abstract class _AlbumFilterData implements AlbumFilterData {
  const factory _AlbumFilterData(
      {required final int id,
      required final String name,
      required final AlbumFilteDataType type}) = _$AlbumFilterDataImpl;

  @override
  int get id;
  @override
  String get name;
  @override
  AlbumFilteDataType get type;

  /// Create a copy of AlbumFilterData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AlbumFilterDataImplCopyWith<_$AlbumFilterDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
