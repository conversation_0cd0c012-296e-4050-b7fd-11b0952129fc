import 'package:flower_timemachine/models/photo_record.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'album_export_controller.g.dart';

@riverpod
class AlbumExportController extends _$AlbumExportController {
  List<PhotoRecord> get selectedPhotos => state;

  void add(PhotoR<PERSON><PERSON> photo) {
    state = [...state, photo];
  }

  void remove(PhotoRecord photo) {
    final index = state.indexWhere((p) => p.id == photo.id);
    if (index != -1) {
      state = [
        for (var i = 0; i < state.length; i++)
          if (i != index) state[i]
      ];
    }
  }

  void clear() {
    state = [];
  }

  @override
  List<PhotoRecord> build() {
    return [];
  }
}
