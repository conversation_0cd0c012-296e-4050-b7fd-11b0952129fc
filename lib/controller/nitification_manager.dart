import 'dart:math';

import 'package:flower_timemachine/common/ftm_notification.dart';
import 'package:flower_timemachine/common/global.dart';

import '../models/notification_model.dart';

class NotificationManger {
  static NotificationManger? _instance;

  // <time, <notificationId, number>>
  var _groupNotificationModel = <int, _PairNotificationInfo>{};
  // 通知ID用累加获得
  int idCounter = 0;

  NotificationManger._internal();

  Future<void> _init() async {
    final notificationList = await NotificationModel.getAll();

    for (final item in notificationList) {
      // 同一天的 notification id 是相同的
      final current = _groupNotificationModel[item.alarmTime] ??= _PairNotificationInfo(item.notificationId, 0);
      final currentNum = current.number + 1;
      current.number = currentNum;

      // 记录最大的 id
      idCounter = max(idCounter, item.notificationId);
    }

    // 总不能同时有这么多通知存在吧
    if (idCounter >= 0xffffffff) {
      idCounter = 0;
    }
  }

  update(int flowerId, DateTime? oldAlarm, DateTime? newAlarm) async {
    final db = await DB.get();
    final batch = db.sqlite.batch();

    // 根据设置信息，配置通知的时间
    final alarmHour = ShareConfig.getAlarmHour();
    final alarmMinute = ShareConfig.getAlarmMinute();

    // 转换旧的多少天后提醒为具体日期
    if (oldAlarm != null) {
      final oldAlarmDate = DateTime(oldAlarm.year, oldAlarm.month, oldAlarm.day, alarmHour, alarmMinute);
      final oldAlarmTimestamp = (oldAlarmDate.millisecondsSinceEpoch / 1000).truncate();

      // 如果存在这天的通知
      final notificationInfo = _groupNotificationModel[oldAlarmTimestamp];
      if (notificationInfo != null) {
        // 如果这天只有一个通知，那么删除这个通知
        notificationInfo.number -= 1;
        if (notificationInfo.number <= 0) {
          _groupNotificationModel.remove(oldAlarmTimestamp);
          FTMNotification.cancel(notificationInfo.notificationId);
        }
      }

      // 删除当前的通知记录
      NotificationModel.deleteOnBatch(batch, flowerId);
    }

    // 计算新通知的日期
    if (newAlarm != null) {
      final newAlarmDate = DateTime(newAlarm.year, newAlarm.month, newAlarm.day, alarmHour, alarmMinute);
      final newAlarmTimestamp = (newAlarmDate.millisecondsSinceEpoch / 1000).truncate();

      final now = DateTime.now();
      final nowTimeStamp = (now.millisecondsSinceEpoch / 1000).truncate();

      if (newAlarmTimestamp - nowTimeStamp > 0) {
        final int newNotificationId;

        // 如果没有这天的通知，创建一个
        var notificationInfo = _groupNotificationModel[newAlarmTimestamp];
        if (notificationInfo == null) {
          newNotificationId = getAndIncrementNotificationId();
          FTMNotification.sendAfter(newNotificationId, newAlarmDate);

          notificationInfo = _PairNotificationInfo(newNotificationId, 1);
          _groupNotificationModel[newAlarmTimestamp] = notificationInfo;
        } else {
          newNotificationId = notificationInfo.notificationId;
          notificationInfo.number += 1;
        }

        NotificationModel.createOnBatch(batch, flowerId, newAlarmTimestamp, newNotificationId);
      }
    }

    await batch.commit(noResult: true);
  }

  updateAll() async {
    await FTMNotification.cancelAll();

    final db = await DB.get();
    final batch = db.sqlite.batch();

    // 根据设置信息，配置通知的时间
    final alarmHour = ShareConfig.getAlarmHour();
    final alarmMinute = ShareConfig.getAlarmMinute();

    final nowTimeStamp = (DateTime.now().millisecondsSinceEpoch / 1000).truncate();

    final newGroup = <int, _PairNotificationInfo>{};
    for (final alarmTimeStamp in _groupNotificationModel.keys) {
      final alarmDate = DateTime.fromMillisecondsSinceEpoch(alarmTimeStamp * 1000);
      final newAlarmDate = DateTime(alarmDate.year, alarmDate.month, alarmDate.day, alarmHour, alarmMinute);
      final newAlarmTimeStamp = (newAlarmDate.millisecondsSinceEpoch / 1000).truncate();

      final item = _groupNotificationModel[alarmTimeStamp]!;

      NotificationModel.updateByTimeOnBatch(batch, alarmTimeStamp, newAlarmTimeStamp);

      if (newAlarmTimeStamp - nowTimeStamp > 0) {
        FTMNotification.sendAfter(item.notificationId, newAlarmDate);
      }

      newGroup[newAlarmTimeStamp] = item;
    }

    _groupNotificationModel = newGroup;

    await batch.commit(noResult: true);
  }

  static Future<NotificationManger> get instance async {
    if (_instance != null) {
      return _instance!;
    }

    final self = NotificationManger._internal();
    _instance = self;

    await self._init();

    return self;
  }

  int getAndIncrementNotificationId() {
    idCounter += 1;
    if (idCounter >= 0xffffffff) {
      idCounter = 0;
    }

    return idCounter;
  }
}

class _PairNotificationInfo {
  final int notificationId;
  int number;

  _PairNotificationInfo(this.notificationId, this.number);
}