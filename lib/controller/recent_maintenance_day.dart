import 'package:flower_timemachine/models/nurture_types.dart';

import '../models/next_task_time.dart';


class RecentMaintenanceDay {
  final List<NurtureType> types;
  final int day;
  // 记录下次养护时间（时分秒清零）
  final DateTime maintenanceTime;

  RecentMaintenanceDay(this.types, this.day, this.maintenanceTime);

  /// 计算最近的养护任务距离现在的天数，如果有多个返回多个
  static RecentMaintenanceDay? calc(List<NextMaintenanceTime> nextTimes) {

    final now = DateTime.now();
    final formattedNow = DateTime(now.year, now.month, now.day);

    int? minDay;
    List<NurtureType> types = [];
    DateTime? maintenanceTime;

    for(final time in nextTimes) {
      final nextTimeDateTime = DateTime.fromMillisecondsSinceEpoch(time.nextTime * 1000);
      // 时分秒清零
      final formattedNextTime = DateTime(nextTimeDateTime.year, nextTimeDateTime.month, nextTimeDateTime.day);
      final nextDay = formattedNextTime.difference(formattedNow).inDays;

      if (minDay == null || nextDay < minDay) {
        minDay = nextDay;
        types = [time.type];
        maintenanceTime = formattedNextTime;
      } else if (minDay == nextDay) {
        types.add(time.type);
      }
    }

    if (minDay == null || maintenanceTime == null) {
      return null;
    }

    return RecentMaintenanceDay(types, minDay, maintenanceTime);
  }
}
