import 'package:easy_localization/easy_localization.dart';

enum MaintenanceType {
  watering,
  fertilize,
  pruning,
  pestControl,
}

extension MaintenanceTypeString on MaintenanceType {
  static final _text = [
    'care_type.water',
    'care_type.fertilizer',
    'care_type.cut',
    'care_type.pest_control'
  ];

  String toText() {
    final t = _text[index];
    // 兼容之前部分用户数据库存储的是已经国际化的名字
    if (t.startsWith('care_type.')) {
      return t.tr();
    } else {
      return t;
    }
  }
}
