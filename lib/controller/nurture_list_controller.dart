import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/date_utils.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/models/maintenance_record.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class NurtureDateInfo {
  final String year;
  final String date;
  final String datePrompt;

  NurtureDateInfo(this.year, this.date, this.datePrompt);
}

class NurtureListController extends StateNotifier<AsyncValue<List<MaintenanceRecord>>> {
  static const _limit = 50;
  final Flower _flower;

  bool _hasMore = true;
  bool _isLoading = false;

  NurtureListController(this._flower) : super(const AsyncValue.loading()) {
    _init();
  }

  void _init() async {
    final result = await MaintenanceRecord.get(_flower.id, offset: 0, limit: _limit);
    state = AsyncValue.data(result);
  }

  Future<int> insert(NurtureType type, DateTime time, String? remark) async {
    final ret = await MaintenanceRecord.insertByType(_flower.id, type, time, remark);
    if (ret <= 0) {
      return ret;
    }

    await _flower.recalcTaskRecord();

    final record = MaintenanceRecord(
      id: ret,
      flowerId: _flower.id,
      type: type,
      time: (time.millisecondsSinceEpoch / 1000).truncate(),
      remark: remark,
    );

    final rows = await MaintenanceRecord.getRowNumber(_flower.id, [ret]);

    _insertRecords(rows, [record]);

    return ret;
  }

  Future<List<MaintenanceRecord>> bulkInsert(List<NurtureType> types, String? remark) async {
    final now = DateTime.now();
    final nowTimeStamp = (now.millisecondsSinceEpoch / 1000).truncate();
    final insertRecords = await MaintenanceRecord.batchInsertByTypes(_flower.id, types, nowTimeStamp, remark);
    if (insertRecords.isEmpty) {
      return insertRecords;
    }

    await _flower.recalcTaskRecord();

    final insertIds = insertRecords.map((e) => e.id!).toList();
    final rows = (await MaintenanceRecord.getRowNumber(_flower.id, insertIds));

    _insertRecords(rows, insertRecords);

    return insertRecords;
  }

  Future<int> deleteRecord(MaintenanceRecord record) async {
    final ret = await record.delete();
    if (ret <= 0) {
      return ret;
    }

    await _flower.recalcTaskRecord();

    state = AsyncValue.data([
      for (final cur in items)
        if (cur.id != record.id) cur
    ]);

    return ret;
  }

  Future<void> loadMore() async {
    if (_isLoading) {
      return;
    }
    _isLoading = true;

    final offset = state.requireValue.length;
    final newData = await MaintenanceRecord.get(_flower.id, offset: offset, limit: _limit);

    _hasMore = newData.length == _limit;

    state = AsyncValue.data([...state.requireValue, ...newData]);

    _isLoading = false;
  }

  static NurtureDateInfo calcDateInfo(MaintenanceRecord record) {
    final dateFormatter = DateFormat.MMMd();
    final t = DateTime.fromMillisecondsSinceEpoch(record.time * 1000);

    return NurtureDateInfo(t.year.toString(), dateFormatter.format(t), calcDatePrompt(t, DateTime.now()));
  }

  void _insertRecords(List<int> rows, List<MaintenanceRecord> records) {
    final oldData = state.requireValue;
    List<MaintenanceRecord>? newData;
    for (int i = 0; i < rows.length; i++) {
      final row = rows[i];
      final insertIndex = row - 1;
      // 还没加载到这里
      if (insertIndex > oldData.length) {
        continue;
      }

      newData ??= [...oldData];

      newData.insert(insertIndex, records[i]);
    }

    if (newData != null) {
      state = AsyncValue.data(newData);
    }
  }

  List<MaintenanceRecord> get items => List.unmodifiable(state.requireValue);

  bool get hasMore => _hasMore;
}

final nurtureListControllerControllerProvider = StateNotifierProvider.autoDispose
    .family<NurtureListController, AsyncValue<List<MaintenanceRecord>>, Flower>(
        (ref, flower) => NurtureListController(flower));
