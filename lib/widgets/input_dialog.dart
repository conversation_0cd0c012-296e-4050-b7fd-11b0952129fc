import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class InputDialog extends StatelessWidget {
  final String title;
  final String? hintText;
  final String initialValue;
  final bool autofocus;

  const InputDialog({
    super.key,
    required this.title,
    this.hintText,
    this.initialValue = '',
    this.autofocus = true,
  });

  static Future<String?> show(
    BuildContext context, {
    required String title,
    String? hintText,
    String initialValue = '',
    bool autofocus = true,
  }) async {
    final result = await showDialog<String>(
      context: context,
      builder: (context) => InputDialog(
        title: title,
        hintText: hintText,
        initialValue: initialValue,
        autofocus: autofocus,
      ),
    );

    return result?.trim().isNotEmpty == true ? result!.trim() : null;
  }

  @override
  Widget build(BuildContext context) {
    final controller = TextEditingController(text: initialValue);

    return AlertDialog(
      title: Center(child: Text(title)),
      content: TextField(
        controller: controller,
        decoration: InputDecoration(
          hintText: hintText ?? title,
        ),
        autofocus: autofocus,
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('cancel').tr(),
        ),
        TextButton(
          onPressed: () => Navigator.pop(context, controller.text),
          child: const Text('confirm').tr(),
        ),
      ],
    );
  }
}
