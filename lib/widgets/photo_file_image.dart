import 'dart:typed_data';

import 'package:flutter/material.dart';

class PhotoFileImage extends StatelessWidget {
  const PhotoFileImage({super.key, required this.imageLoader, required this.onLoad});

  final Future<Uint8List?> imageLoader;
  final Widget Function(Uint8List) onLoad;


  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: imageLoader,
      builder: (BuildContext context, AsyncSnapshot<Uint8List?> imageFile) {
        if (!imageFile.hasData) {
          return Container();
        }

        final file = imageFile.data;
        if (file == null) {
          return Container();
        } else {
          return onLoad(file);
        }
      }
    );
  }
}
