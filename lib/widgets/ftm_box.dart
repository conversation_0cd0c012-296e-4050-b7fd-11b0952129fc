import 'package:flutter/material.dart';

class FTMBox extends StatelessWidget {
  const FTMBox({super.key, required this.circular, required this.child, this.color });

  final double circular;
  final Widget child;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    // return DecoratedBox(
    //   decoration: BoxDecoration(
    //     border: Border.all(color: const Color(0xFFCBE0B3), width: 2),
    //     borderRadius: BorderRadius.all(Radius.circular(circular)),
    //   ),
    //   child: child,
    // );

    return Card(
      color: color,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(circular)),
          side: const BorderSide(color: Color(0xFFCBE0B3))),
      child: child,
    );
  }
}
