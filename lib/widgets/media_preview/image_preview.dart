import 'dart:io';

import 'package:flutter/material.dart';

class ImagePreview extends StatelessWidget {
  const ImagePreview({
    super.key,
    required this.path,
    this.width,
    this.height,
    this.radius,
    this.fit,
    this.heroTag,
  });


  final String path;
  final double? width;
  final double? height;
  final BorderRadius? radius;
  final BoxFit? fit;
  final Object? heroTag;

  @override
  Widget build(BuildContext context) {
    ImageProvider image;
    if (width != null) {
      image = ResizeImage(
        FileImage(File(path)),
        width: (width! * 2).toInt(),
        policy: ResizeImagePolicy.fit,
      );
    } else {
      image = FileImage(File(path));
    }

    final widget = ClipRRect(
      borderRadius: radius ?? BorderRadius.zero,
      child: Image(
        image: image,
        width: width,
        height: height,
        fit: fit ?? BoxFit.cover,
      ),
    );

    if (heroTag != null) {
      return Hero(
        tag: heroTag!,
        child: widget,
      );
    }

    return widget;
  }
}