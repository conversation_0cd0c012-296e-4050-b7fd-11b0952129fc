import 'dart:io';

import 'package:flutter/material.dart';

class VideoPreview extends StatelessWidget {
  const VideoPreview({
    super.key,
    required this.path,
    this.width,
    this.height,
    this.radius,
    this.fit,
    this.heroTag,
  });

  final String path;
  final double? width;
  final double? height;
  final BorderRadius? radius;
  final BoxFit? fit;
  final Object? heroTag;

  @override
  Widget build(BuildContext context) {
    ImageProvider image;
    if (width != null) {
      image = ResizeImage(
        FileImage(File(path)),
        width: (width! * 2).toInt(),
        policy: ResizeImagePolicy.fit,
      );
    } else {
      image = FileImage(File(path));
    }

    final imageWidget = Image(
      image: image,
      width: width,
      height: height,
      fit: fit ?? BoxFit.cover,
    );

    final stackWidget = Stack(
      children: [
        imageWidget,
        Positioned(
          right: 8,
          bottom: 8,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.6),
              shape: BoxShape.circle,
            ),
            padding: const EdgeInsets.all(4),
            child: const Icon(
              Icons.play_arrow,
              color: Colors.white,
              size: 16,
            ),
          ),
        ),
      ],
    );

    final widget = ClipRRect(
      borderRadius: radius ?? BorderRadius.zero,
      child: stackWidget,
    );

    if (heroTag != null) {
      return Hero(
        tag: heroTag!,
        child: widget,
      );
    }

    return widget;
  }
}
