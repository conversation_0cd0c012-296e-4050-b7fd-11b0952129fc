import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class ErrorReporter extends StatelessWidget {
  const ErrorReporter({super.key, required this.error, required this.stackTrace});

  final Object error;
  final StackTrace stackTrace;

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      return Center(child: Text(error.toString()));
    }

    return FutureBuilder(
      builder: (BuildContext context, AsyncSnapshot<SentryId> snapshot) {
        if (snapshot.connectionState != ConnectionState.done) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError) {
          return const Text('system_error_and_restart').tr();
        }

        final errorId = snapshot.data.toString();

        return Padding(
            padding: const EdgeInsets.all(4), child: const Text('system_error_code').tr(namedArgs: {"code": errorId}));
      },
      future: Sentry.captureException(error, stackTrace: stackTrace),
    );
  }
}
