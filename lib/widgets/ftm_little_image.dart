import 'dart:io';

import 'package:flutter/material.dart';

class FTMLittleImage extends StatelessWidget {
  final String imageFile;

  final double? minWidth;
  final double? minHeight;

  const FTMLittleImage({super.key, required this.imageFile, this.minHeight, this.minWidth});

  @override
  Widget build(BuildContext context) {
    return Image(
      image: ResizeImage(
        FileImage(File(imageFile)),
        width: minWidth == null ? null : (minWidth! * 1.5).toInt(),
        policy: ResizeImagePolicy.fit,
        // height: minHeight == null ? null : (minHeight! * 1.5).toInt(),
      ),
      gaplessPlayback: true,
      fit: BoxFit.cover,
    );
    // return FutureBuilder(
    //   builder: (BuildContext context, AsyncSnapshot<Uint8List?> imageData) {
    //     if (imageData.connectionState != ConnectionState.done) {
    //       return const Center(child: CircularProgressIndicator());
    //     }
    //
    //     return Image.memory(
    //       imageData.data!,
    //       fit: BoxFit.cover,
    //       width: minWidth,
    //       height: minHeight,
    //     );
    //   },
    //   future: loadImageThumbnail()
    // );
  }

  // Future<Uint8List?> loadImageThumbnail() async {
  //   return await FlutterImageCompress.compressWithFile(
  //     imageFile,
  //     minHeight: 256,
  //     minWidth: 256,
  //   );
  // }
}
