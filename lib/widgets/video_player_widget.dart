import 'dart:io';

import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

class VideoPlayerWidget extends StatefulWidget {
  const VideoPlayerWidget({
    super.key,
    required this.videoPath,
    this.autoPlay = false,
    this.showControls = true,
    this.looping = false,
    this.muted = false,
    this.isVisible = true,
  });

  final String videoPath;
  final bool autoPlay;
  final bool showControls;
  final bool looping;
  final bool muted;
  final bool isVisible;

  @override
  State<VideoPlayerWidget> createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> with WidgetsBindingObserver {
  late VideoPlayerController _controller;
  bool _isInitialized = false;
  bool _isPlaying = false;
  double _currentPosition = 0;
  double _totalDuration = 0;
  bool _wasPlaying = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeVideoPlayer();
  }

  @override
  void didUpdateWidget(VideoPlayerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 当isVisible变化时控制视频播放状态
    if (oldWidget.isVisible != widget.isVisible) {
      if (widget.isVisible) {
        // 如果变为可见且需要自动播放，则播放视频
        if (widget.autoPlay && _isInitialized) {
          _controller.play();
          setState(() {
            _isPlaying = true;
          });
        }
      } else {
        // 如果变为不可见，则暂停视频
        if (_isPlaying && _isInitialized) {
          _wasPlaying = true;
          _controller.pause();
          setState(() {
            _isPlaying = false;
          });
        }
      }
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // 当应用进入后台时暂停视频
    if (state == AppLifecycleState.paused) {
      if (_isPlaying) {
        _wasPlaying = true;
        _controller.pause();
        setState(() {
          _isPlaying = false;
        });
      }
    }
    // 当应用返回前台时，如果之前在播放且当前可见，则恢复播放
    else if (state == AppLifecycleState.resumed) {
      if (_wasPlaying && widget.isVisible) {
        _controller.play();
        setState(() {
          _isPlaying = true;
          _wasPlaying = false;
        });
      }
    }
  }

  Future<void> _initializeVideoPlayer() async {
    _controller = VideoPlayerController.file(File(widget.videoPath));

    await _controller.initialize();

    if (mounted) {
      // 如果需要静音，设置音量为0
      if (widget.muted) {
        _controller.setVolume(0);
      }

      setState(() {
        _isInitialized = true;
        _totalDuration = _controller.value.duration.inMilliseconds.toDouble();

        if (widget.autoPlay) {
          _controller.play();
          _isPlaying = true;
        }

        if (widget.looping) {
          _controller.setLooping(true);
        }
      });

      // 监听播放进度
      _controller.addListener(_videoListener);
    }
  }

  void _videoListener() {
    if (mounted && _controller.value.isInitialized) {
      setState(() {
        _currentPosition = _controller.value.position.inMilliseconds.toDouble();
        _isPlaying = _controller.value.isPlaying;
      });
    }
  }

  @override
  void dispose() {
    // 先移除监听器，避免在组件销毁后仍然触发setState
    _controller.removeListener(_videoListener);

    // 确保在销毁前停止视频播放，避免声音重叠问题
    if (_controller.value.isPlaying) {
      _controller.pause();
    }

    _controller.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Stack(
      alignment: Alignment.center,
      children: [
        // 视频内容
        AspectRatio(
          aspectRatio: _controller.value.aspectRatio,
          child: VideoPlayer(_controller),
        ),

        // 播放/暂停按钮
        if (!_isPlaying && widget.showControls)
          GestureDetector(
            onTap: () {
              _controller.play();
              setState(() {
                _isPlaying = true;
              });
            },
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.play_arrow,
                color: Colors.white,
                size: 32,
              ),
            ),
          ),

        // 控制条
        if (widget.showControls)
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.5),
                  ],
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 进度条
                  SliderTheme(
                    data: SliderThemeData(
                      trackHeight: 2,
                      thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
                      overlayShape: const RoundSliderOverlayShape(overlayRadius: 12),
                      activeTrackColor: Colors.white,
                      inactiveTrackColor: Colors.white.withOpacity(0.3),
                      thumbColor: Colors.white,
                      overlayColor: Colors.white.withOpacity(0.3),
                    ),
                    child: Slider(
                      value: _currentPosition,
                      min: 0,
                      max: _totalDuration,
                      onChanged: (value) {
                        setState(() {
                          _currentPosition = value;
                        });
                        _controller.seekTo(Duration(milliseconds: value.toInt()));
                      },
                    ),
                  ),

                  // 时间和控制按钮
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // 时间显示
                        Text(
                          '${_formatDuration(_controller.value.position)} / ${_formatDuration(_controller.value.duration)}',
                          style: const TextStyle(color: Colors.white, fontSize: 12),
                        ),

                        // 控制按钮
                        Row(
                          children: [
                            // 播放/暂停按钮
                            IconButton(
                              icon: Icon(
                                _isPlaying ? Icons.pause : Icons.play_arrow,
                                color: Colors.white,
                              ),
                              onPressed: _onPressed,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes.remainder(60).toString().padLeft(2, '0');
    final seconds = duration.inSeconds.remainder(60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  void _onPressed()  {
    final isPlaying = _isPlaying;
    setState(() {
      _isPlaying = !isPlaying;
    });
    if (isPlaying) {
      _controller.pause();
    } else {
      _controller.play();
    }
  }
}
