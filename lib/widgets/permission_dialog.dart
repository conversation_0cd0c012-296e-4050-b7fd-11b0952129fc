import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:app_settings/app_settings.dart';

class PermissionDialog extends StatelessWidget {
  const PermissionDialog({super.key, required this.title});

  final String title;

  @override
  Widget build(BuildContext context) {
    return CupertinoAlertDialog(
      title: Text('permission_dialog.no_permission'.tr()),
      content: Text(title),
      actions: <CupertinoDialogAction>[
        CupertinoDialogAction(
          onPressed: () => Navigator.pop(context, false),
          child: Text('cancel'.tr()),
        ),
        CupertinoDialogAction(
          onPressed: () => Navigator.pop(context, true),
          textStyle: const TextStyle(color: Colors.blueAccent),
          child: const Text('permission_dialog.go_setting').tr(),
        ),
      ],
    );
  }

  static Future<void> show(BuildContext context, String title) async {
    final ret = await showCupertinoModalPopup<bool>(
      context: context,
      builder: (BuildContext context) => PermissionDialog(title: title)
    );

    if (!(ret ?? false)) {
      return;
    }

    await AppSettings.openAppSettings(type: AppSettingsType.location);
  }
}