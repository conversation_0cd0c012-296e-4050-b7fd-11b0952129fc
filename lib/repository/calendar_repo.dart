import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/models/flower_timeline.dart';
import 'package:flower_timemachine/models/maintenance_record.dart';
import 'package:flower_timemachine/models/photo_record.dart';
import 'package:flower_timemachine/types/calendar_type.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'calendar_repo.g.dart';

class CalendarRepo {
  Future<List<CalendarState>> getCalendarEvent(DateTime day, int limit, int offset) async {
    final db = await DB.get();
    final dayStart = DateTime(day.year, day.month, day.day).millisecondsSinceEpoch ~/ 1000;
    final dayEnd = DateTime(day.year, day.month, day.day + 1).millisecondsSinceEpoch ~/ 1000;

    // 联合查询时光机和养护记录的id
    final timelineQuery = """
        SELECT 
        ${FlowerTimeline.idField}, ${FlowerTimeline.flowerIdField}, ${FlowerTimeline.timeField} as time, 'timeline' as queryType
        FROM ${FlowerTimeline.tableName} where ${FlowerTimeline.timeField} >= $dayStart AND ${FlowerTimeline.timeField} < $dayEnd
        """;

    final nurtureQuery = """
        SELECT 
        ${MaintenanceRecord.idField}, ${FlowerTimeline.flowerIdField}, ${MaintenanceRecord.timeField} as time, 'nurture' as queryType
        FROM ${MaintenanceRecord.tableName} where ${MaintenanceRecord.timeField} >= $dayStart AND ${MaintenanceRecord.timeField} < $dayEnd
        """;

    final query = await db.sqlite.rawQuery(
      """
      $timelineQuery
      UNION ALL
      $nurtureQuery
      ORDER BY time ASC
      LIMIT $limit
      OFFSET $offset
      """,
    );

    // 批量查询时光机 / 养护记录 和 flower 的内容
    final batch = db.sqlite.batch();
    for (final item in query) {
      final type = item['queryType'] as String;

      String flowerIdField;
      if (type == 'timeline') {
        flowerIdField = FlowerTimeline.flowerIdField;
        final timelineId = item[FlowerTimeline.idField];
        batch.rawQuery(
          _buildTimelineQuery(),
          [timelineId, timelineId],
        );
      } else {
        flowerIdField = MaintenanceRecord.flowerIdField;
        batch.rawQuery(
          _buildNurtureQuery(),
          [item[MaintenanceRecord.idField]],
        );
      }

      batch.query(
        Flower.tableName,
        where: '${Flower.idField} = ?',
        whereArgs: [item[flowerIdField]],
      );
    }

    final result = await batch.commit();

    List<CalendarState> calendarStates = [];

    // 从批量查询解析结果
    final resultIter = result.iterator;
    while (resultIter.moveNext()) {
      final item = (resultIter.current as List<Map<String, dynamic>>).first;
      final type = item['queryType'] as String?;

      resultIter.moveNext();
      final flowerItem = (resultIter.current as List<Map<String, dynamic>>).first;

      final flower = await Flower.createFromDBRow(flowerItem);

      if (type == 'timeline') {
        final timeline = FlowerTimeline.fromDB(item);
        final photo = item['photos'] as String?;
        calendarStates.add(CalendarState(
          timeline: timeline,
          type: CalendarType.timeline,
          time: DateTime.fromMillisecondsSinceEpoch(timeline.time * 1000),
          nurture: null,
          photos: photo != null ? await PhotoRecord.parsePhotos(photo) : null,
          flower: flower,
        ));
      } else {
        final nurture = MaintenanceRecord.fromDB(item);
        calendarStates.add(CalendarState(
          timeline: null,
          type: CalendarType.nurture,
          time: DateTime.fromMillisecondsSinceEpoch(nurture.time * 1000),
          nurture: nurture,
          photos: null,
          flower: flower,
        ));
      }
    }

    return calendarStates;
  }

  Future<Map<DateTime, bool>> getCalendarMarkedDates(DateTime month) async {
    final db = await DB.get();
    final monthStart = DateTime(month.year, month.month, 1).millisecondsSinceEpoch ~/ 1000;
    final monthEnd = DateTime(month.year, month.month + 1, 0).millisecondsSinceEpoch ~/ 1000;

    final timelineQuery = """
      SELECT DISTINCT date(${FlowerTimeline.timeField}, 'unixepoch', 'localtime') as date
      FROM ${FlowerTimeline.tableName} 
      WHERE ${FlowerTimeline.timeField} >= $monthStart AND ${FlowerTimeline.timeField} < $monthEnd
    """;

    final nurtureQuery = """
      SELECT DISTINCT date(${MaintenanceRecord.timeField}, 'unixepoch', 'localtime') as date
      FROM ${MaintenanceRecord.tableName} 
      WHERE ${MaintenanceRecord.timeField} >= $monthStart AND ${MaintenanceRecord.timeField} < $monthEnd
    """;

    final query = await db.sqlite.rawQuery(
      """
      $timelineQuery
      UNION
      $nurtureQuery
      """,
    );

    final Map<DateTime, bool> markedDates = {};
    for (final item in query) {
      final dateStr = item['date'] as String;
      final date = DateTime.parse(dateStr);
      markedDates[date] = true;
    }

    return markedDates;
  }

  static _buildTimelineQuery() {
    return """
      SELECT *, 'timeline' as queryType, (${_buildTimelineFilesQuery()}) as photos
      FROM ${FlowerTimeline.tableName} WHERE ${FlowerTimeline.idField} = ?
    """;
  }

  static _buildTimelineFilesQuery() {
    return """
      SELECT GROUP_CONCAT(${PhotoRecord.fileField}) FROM ${PhotoRecord.tableName} WHERE ${PhotoRecord.flowerTimelineIdField} = ?
    """;
  }

  static _buildNurtureQuery() {
    return "SELECT *, 'nurture' as queryType FROM ${MaintenanceRecord.tableName} WHERE ${MaintenanceRecord.idField} = ?";
  }
}

@riverpod
CalendarRepo calendarRepo(CalendarRepoRef ref) {
  return CalendarRepo();
}
