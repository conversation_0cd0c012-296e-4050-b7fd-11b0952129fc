import 'package:flower_timemachine/models/nurture_types.dart';

import 'maintenance_record.dart';

class LastMaintenanceTime {
  final List<NurtureType> types;
  final int day;
  // 记录最近养护时间（时分秒清零）
  final DateTime maintenanceTime;

  LastMaintenanceTime(this.types, this.day, this.maintenanceTime);

  /// 计算最近的养护任务距离现在的天数，如果有多个返回多个
  static LastMaintenanceTime? calc(List<MaintenanceRecord> records) {
    if (records.isEmpty) {
      return null;
    }

    final now = DateTime.now();
    final formattedNow = DateTime(now.year, now.month, now.day);

    int? maxDay;
    List<NurtureType> types = [];
    DateTime? maintenanceTime;

    for (final record in records) {
      final recordDateTime = DateTime.fromMillisecondsSinceEpoch(record.time * 1000);
      // 时分秒清零
      final formattedRecordTime = DateTime(recordDateTime.year, recordDateTime.month, recordDateTime.day);
      final lastDay = formattedNow.difference(formattedRecordTime).inDays;

      if (maxDay == null || lastDay > maxDay) {
        maxDay = lastDay;
        types = [record.type];
        maintenanceTime = formattedRecordTime;
      } else if (maxDay == lastDay) {
        types.add(record.type);
      }
    }

    if (maxDay == null || maintenanceTime == null) {
      return null;
    }

    return LastMaintenanceTime(types, maxDay, maintenanceTime);
  }
}
