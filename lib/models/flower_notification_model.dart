

import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/models/notification_model.dart';

class FlowerNotificationModel {
  final int alarmTime;
  final int notificationId;
  final int flowerId;
  final String flowerName;
  final String? flowerAvatar;

  FlowerNotificationModel({
    required this.alarmTime,
    required this.notificationId,
    required this.flowerId,
    required this.flowerName,
    required this.flowerAvatar
  });

  static FlowerNotificationModel createFromDBRow(Map<String, dynamic> row) {
    return FlowerNotificationModel(
      alarmTime: row['alarmTime'],
      notificationId: row['notificationId'],
      flowerId: row['flowerId'],
      flowerName: row['name'],
      flowerAvatar: row['avatar'],
    );
  }

  static Future<Iterable<FlowerNotificationModel>> get(int limit, int offset) async {
    final db = await DB.get();

    final List<Map<String, dynamic>> result = await db.sqlite.rawQuery(
        'SELECT * from ${NotificationModel.tableName} as n '
        'JOIN ${Flower.tableName} as f ON n.flowerId = f.id '
        'ORDER BY alarmTime DESC'
    );

    return result.map((e) => createFromDBRow(e)).toList();
  }
}
