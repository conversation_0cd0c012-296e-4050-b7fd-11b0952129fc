import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_version.g.dart';

part 'app_version.freezed.dart';

@freezed
sealed class VersionUpdate with _$VersionUpdate {
  const factory VersionUpdate({
    String? Version,
    int? VersionCode,
    String? Description,
    int? Platform,
    bool? Force,
    String? URL,
  }) = _VersionUpdate;

  factory VersionUpdate.fromJson(Map<String, dynamic> json) => _$VersionUpdateFromJson(json);
}
