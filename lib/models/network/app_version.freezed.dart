// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_version.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

VersionUpdate _$VersionUpdateFromJson(Map<String, dynamic> json) {
  return _VersionUpdate.fromJson(json);
}

/// @nodoc
mixin _$VersionUpdate {
  String? get Version => throw _privateConstructorUsedError;
  int? get VersionCode => throw _privateConstructorUsedError;
  String? get Description => throw _privateConstructorUsedError;
  int? get Platform => throw _privateConstructorUsedError;
  bool? get Force => throw _privateConstructorUsedError;
  String? get URL => throw _privateConstructorUsedError;

  /// Serializes this VersionUpdate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VersionUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VersionUpdateCopyWith<VersionUpdate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VersionUpdateCopyWith<$Res> {
  factory $VersionUpdateCopyWith(
          VersionUpdate value, $Res Function(VersionUpdate) then) =
      _$VersionUpdateCopyWithImpl<$Res, VersionUpdate>;
  @useResult
  $Res call(
      {String? Version,
      int? VersionCode,
      String? Description,
      int? Platform,
      bool? Force,
      String? URL});
}

/// @nodoc
class _$VersionUpdateCopyWithImpl<$Res, $Val extends VersionUpdate>
    implements $VersionUpdateCopyWith<$Res> {
  _$VersionUpdateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VersionUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? Version = freezed,
    Object? VersionCode = freezed,
    Object? Description = freezed,
    Object? Platform = freezed,
    Object? Force = freezed,
    Object? URL = freezed,
  }) {
    return _then(_value.copyWith(
      Version: freezed == Version
          ? _value.Version
          : Version // ignore: cast_nullable_to_non_nullable
              as String?,
      VersionCode: freezed == VersionCode
          ? _value.VersionCode
          : VersionCode // ignore: cast_nullable_to_non_nullable
              as int?,
      Description: freezed == Description
          ? _value.Description
          : Description // ignore: cast_nullable_to_non_nullable
              as String?,
      Platform: freezed == Platform
          ? _value.Platform
          : Platform // ignore: cast_nullable_to_non_nullable
              as int?,
      Force: freezed == Force
          ? _value.Force
          : Force // ignore: cast_nullable_to_non_nullable
              as bool?,
      URL: freezed == URL
          ? _value.URL
          : URL // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VersionUpdateImplCopyWith<$Res>
    implements $VersionUpdateCopyWith<$Res> {
  factory _$$VersionUpdateImplCopyWith(
          _$VersionUpdateImpl value, $Res Function(_$VersionUpdateImpl) then) =
      __$$VersionUpdateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? Version,
      int? VersionCode,
      String? Description,
      int? Platform,
      bool? Force,
      String? URL});
}

/// @nodoc
class __$$VersionUpdateImplCopyWithImpl<$Res>
    extends _$VersionUpdateCopyWithImpl<$Res, _$VersionUpdateImpl>
    implements _$$VersionUpdateImplCopyWith<$Res> {
  __$$VersionUpdateImplCopyWithImpl(
      _$VersionUpdateImpl _value, $Res Function(_$VersionUpdateImpl) _then)
      : super(_value, _then);

  /// Create a copy of VersionUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? Version = freezed,
    Object? VersionCode = freezed,
    Object? Description = freezed,
    Object? Platform = freezed,
    Object? Force = freezed,
    Object? URL = freezed,
  }) {
    return _then(_$VersionUpdateImpl(
      Version: freezed == Version
          ? _value.Version
          : Version // ignore: cast_nullable_to_non_nullable
              as String?,
      VersionCode: freezed == VersionCode
          ? _value.VersionCode
          : VersionCode // ignore: cast_nullable_to_non_nullable
              as int?,
      Description: freezed == Description
          ? _value.Description
          : Description // ignore: cast_nullable_to_non_nullable
              as String?,
      Platform: freezed == Platform
          ? _value.Platform
          : Platform // ignore: cast_nullable_to_non_nullable
              as int?,
      Force: freezed == Force
          ? _value.Force
          : Force // ignore: cast_nullable_to_non_nullable
              as bool?,
      URL: freezed == URL
          ? _value.URL
          : URL // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VersionUpdateImpl implements _VersionUpdate {
  const _$VersionUpdateImpl(
      {this.Version,
      this.VersionCode,
      this.Description,
      this.Platform,
      this.Force,
      this.URL});

  factory _$VersionUpdateImpl.fromJson(Map<String, dynamic> json) =>
      _$$VersionUpdateImplFromJson(json);

  @override
  final String? Version;
  @override
  final int? VersionCode;
  @override
  final String? Description;
  @override
  final int? Platform;
  @override
  final bool? Force;
  @override
  final String? URL;

  @override
  String toString() {
    return 'VersionUpdate(Version: $Version, VersionCode: $VersionCode, Description: $Description, Platform: $Platform, Force: $Force, URL: $URL)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VersionUpdateImpl &&
            (identical(other.Version, Version) || other.Version == Version) &&
            (identical(other.VersionCode, VersionCode) ||
                other.VersionCode == VersionCode) &&
            (identical(other.Description, Description) ||
                other.Description == Description) &&
            (identical(other.Platform, Platform) ||
                other.Platform == Platform) &&
            (identical(other.Force, Force) || other.Force == Force) &&
            (identical(other.URL, URL) || other.URL == URL));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, Version, VersionCode, Description, Platform, Force, URL);

  /// Create a copy of VersionUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VersionUpdateImplCopyWith<_$VersionUpdateImpl> get copyWith =>
      __$$VersionUpdateImplCopyWithImpl<_$VersionUpdateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VersionUpdateImplToJson(
      this,
    );
  }
}

abstract class _VersionUpdate implements VersionUpdate {
  const factory _VersionUpdate(
      {final String? Version,
      final int? VersionCode,
      final String? Description,
      final int? Platform,
      final bool? Force,
      final String? URL}) = _$VersionUpdateImpl;

  factory _VersionUpdate.fromJson(Map<String, dynamic> json) =
      _$VersionUpdateImpl.fromJson;

  @override
  String? get Version;
  @override
  int? get VersionCode;
  @override
  String? get Description;
  @override
  int? get Platform;
  @override
  bool? get Force;
  @override
  String? get URL;

  /// Create a copy of VersionUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VersionUpdateImplCopyWith<_$VersionUpdateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
