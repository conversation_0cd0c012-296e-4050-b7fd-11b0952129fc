
import 'package:flower_timemachine/common/date_utils.dart';
import 'package:sqflite/sqflite.dart';

import '../common/global.dart';

class NotificationModel {
  static const tableName = 'notification';

  final int alarmTime;
  final int notificationId;
  final int flowerId;

  NotificationModel({required this.alarmTime, required this.notificationId, required this.flowerId});

  static NotificationModel createFromDBRow(Map<String, dynamic> row) {
    return NotificationModel(
      alarmTime: row['alarmTime'],
      notificationId: row['notificationId'],
      flowerId: row['flowerId'],
    );
  }

  static deleteOnBatch(Batch batch, int flowerId) {
    batch.delete(
        tableName,
        where: 'flowerId = ?',
        whereArgs: [flowerId]
    );
  }

  static updateByTimeOnBatch(Batch batch, int alarmTime, int newAlarmTime) {
    batch.update(tableName, {
        'alarmTime': newAlarmTime
      },
      where: 'alarmTime = ?',
      whereArgs: [alarmTime]
    );
  }

  static createOnBatch(Batch batch, int flowerId, int alarmTime, int notificationId) {
    batch.insert(tableName, {
      'alarmTime': alarmTime,
      'notificationId': notificationId,
      'flowerId': flowerId,
    });
  }

  static Future<Iterable<NotificationModel>> getAll() async {
    final db = await DB.get();

    final List<Map<String, dynamic>> result = await db.sqlite.query(NotificationModel.tableName);
    return result.map((row) => NotificationModel.createFromDBRow(row));
  }


  static Future<void> clearOldRecord() async {
    final db = await DB.get();
    final now = (getTodayZeroClock().millisecondsSinceEpoch / 1000).round();

    db.sqlite.execute("DELETE FROM ${tableName} WHERE alarmTime < ?", [now]);
  }


  static String createTable() {
    return 'CREATE TABLE $tableName('
        'alarmTime INTEGER, '
        'notificationId INTEGER, '
        'flowerId INTEGER)';
  }
}