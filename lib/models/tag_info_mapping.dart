import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/models/tag_info.dart';
import 'package:sqflite/sqflite.dart';

class TagInfoMapping {
  static const tableName = 'tag_info_mapping';

  final int id;
  final int flowerId;
  final int tabId;

  TagInfoMapping({required this.id, required this.flowerId, required this.tabId});

  static String createTable() {
    return 'CREATE TABLE $tableName('
        'id INTEGER PRIMARY KEY, '
        'flowerId INTEGER, '
        'tagId INTEGER)';
  }

  static Future<void> create(int flowerId, List<TagInfo> tags) async {
    final db = await DB.get();
    final batch = db.sqlite.batch();

    for (final tag in tags) {
      batch.insert(tableName, {'flowerId': flowerId, 'tagId': tag.id});
    }

    await batch.commit();
  }

  static Future<void> update(int flowerId, List<TagInfo> tags) async {
    final db = await DB.get();
    final batch = db.sqlite.batch();

    batch.delete(tableName, where: 'flowerId = ?', whereArgs: [flowerId]);
    for (final tag in tags) {
      batch.insert(tableName, {'flowerId': flowerId, 'tagId': tag.id});
    }

    await batch.commit();
  }

  static Future<Iterable<TagInfo>> getByFlowerId(int flowerId) async {
    final db = await DB.get();
    List<Map<String, dynamic>> result = await db.sqlite.rawQuery(
      'SELECT tag.* from ${TagInfo.tableName} tag '
      'LEFT JOIN $tableName mapping on mapping.tagId = tag.id '
      'WHERE mapping.flowerId = ?',
      [flowerId],
    );

    return result.map(
      (e) => TagInfo(id: e['id'], name: e['name'], sortOrder: e['sort_order']),
    );
  }

  static Future<int> deleteByTag(int tagId) async {
    final db = await DB.get();
    return await db.sqlite.delete(tableName, where: 'tagId = ?', whereArgs: [tagId]);
  }

  static Future<int> countFlowerTag(int flowerId) async {
    final db = await DB.get();
    return Sqflite.firstIntValue(
            await db.sqlite.rawQuery('select count(id) from $tableName where flowerId = ?', [flowerId])) ??
        0;
  }

  static Future<int> deleteByFlower(int flowerId) async {
    final db = await DB.get();

    return await db.sqlite.delete(tableName, where: 'flowerId = ?', whereArgs: [flowerId]);
  }
}
