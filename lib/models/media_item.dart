import 'package:flower_timemachine/models/photo_record.dart';

/// 媒体项，用于表示图片、视频或 LivePhoto
class MediaItem {
  /// 文件路径
  final String path;

  /// 缩略图路径（对于视频和 LivePhoto）
  final String? thumbnailPath;

  /// 媒体类型
  final MediaType type;

  /// 构造函数
  const MediaItem({
    required this.path,
    this.thumbnailPath,
    this.type = MediaType.image,
  });

  /// 从 PhotoRecord 创建 MediaItem
  static MediaItem fromPhotoRecord(PhotoRecord record) {
    return MediaItem(
      path: record.file,
      thumbnailPath: record.hasThumbnail ? record.thumbnailFile : null,
      type: record.mediaType,
    );
  }

  /// 获取用于显示的路径，优先使用缩略图
  String get displayPath => thumbnailPath ?? path;

  /// 判断是否为视频
  bool get isVideo => type == MediaType.video;

  /// 判断是否为 LivePhoto
  bool get isLivePhoto => type == MediaType.livePhoto;

  /// 判断是否为图片
  bool get isImage => type == MediaType.image;
}
