import 'package:flower_timemachine/common/global.dart';
import 'package:sqflite/sqflite.dart';

class FlowerNotes {

  static const tableName = 'flower_notes';

  final int flowerId;
  final String note;

  FlowerNotes({
    required this.flowerId,
    required this.note,
  });

  static FlowerNotes createFromDBRow(Map<String, dynamic> row) {
    return FlowerNotes(
      flowerId: row['flowerId'],
      note: row['note'],
    );
  }

  static Future<String?> get(int flowerId) async {
    final db = await DB.get();

    final query = await db.sqlite.query(
        tableName, where: "flowerId = ?", whereArgs: [flowerId]
    );

    return query.isNotEmpty ? query.first["note"] as String? : null;
  }

  static Future<void> delete(int flowerId) async {
    final db = await DB.get();
    await db.sqlite.delete(
        tableName, where: "flowerId = ?", whereArgs: [flowerId]
    );
  }

  static Future<void> save(int flowerId, String text) async {
    final db = await DB.get();
    await db.sqlite.insert(
        tableName,
        {
          'flowerId': flowerId,
          'note': text
        },
        conflictAlgorithm: ConflictAlgorithm.replace
    );
  }

  static String createTable() {
    return 'CREATE TABLE $tableName('
        'id INTEGER PRIMARY KEY, '
        'flowerId INTEGER UNIQUE, '
        'note TEXT)';
  }
}
