
import 'package:flower_timemachine/common/date_utils.dart';
import 'package:flower_timemachine/models/flower_timeline.dart';
import 'package:flower_timemachine/models/media_item.dart';
import 'package:flower_timemachine/models/photo_record.dart';


class FlowerTimelineItem {
  final int flowerTimelineId;

  final String? text;

  final List<PhotoRecord> photos;

  // 多少天之前的提示
  final String datePrompt;

  final DateTime dateTime;

  FlowerTimelineItem(
      {required this.flowerTimelineId,
      this.text,
      required this.photos,
      required this.datePrompt,
      required this.dateTime});

  static Future<List<FlowerTimelineItem>> get(int flowerId, {int? offset, required int limit}) async {
    final List<FlowerTimeline> timelines = await FlowerTimeline.get(flowerId, offset: offset, limit: limit);

    if (timelines.isEmpty) {
      return [];
    }

    final now = DateTime.now();

    final List<FlowerTimelineItem> results = [];
    for (final timeline in timelines) {
      final photos = await PhotoRecord.getByFlowerTimelineId(timeline.id);
      final t = DateTime.fromMillisecondsSinceEpoch(timeline.time * 1000);

      results.add(FlowerTimelineItem(
          flowerTimelineId: timeline.id,
          photos: photos,
          datePrompt: calcDatePrompt(t, now),
          text: timeline.text,
          dateTime: t));
    }

    return results;
  }

  static Future<FlowerTimelineItem> createWithMediaItems(int flowerId, String? text, List<MediaItem> mediaItems, DateTime time) async {
    return await createWithPhotoRecords(flowerId, text, mediaItems, time);
  }

  static Future<FlowerTimelineItem> createWithPhotoRecords(int flowerId, String? text, List<MediaItem> photoRecords, DateTime time) async {
    final timeline = await FlowerTimeline.create(flowerId, text, time);
    List<PhotoRecord> savedRecords = [];
    if (photoRecords.isNotEmpty) {
      savedRecords = await PhotoRecord.createList(
          flowerId, timeline.id, photoRecords, time
      );
    }

    final now = DateTime.now();

    return FlowerTimelineItem(
        flowerTimelineId: timeline.id, photos: savedRecords, datePrompt: calcDatePrompt(time, now), dateTime: time
    );
  }

  Future<void> delete() async {
    await PhotoRecord.deleteByFlowerTimelineId(flowerTimelineId);
    await FlowerTimeline.deleteById(flowerTimelineId);
  }

  Future<FlowerTimelineItem> updateWithMediaItems(String? text, List<MediaItem> mediaItems, DateTime time, int flowerId) async {
    return await updateWithPhotoRecords(text, mediaItems, time, flowerId);
  }

  /// 使用 PhotoRecord 列表更新 FlowerTimelineItem
  Future<FlowerTimelineItem> updateWithPhotoRecords(String? text, List<MediaItem> photoRecords, DateTime time, int flowerId) async {
    await FlowerTimeline.update(flowerTimelineId, text, time);
    final savedRecords = await PhotoRecord.updateByFlowerTimelineId(
        flowerId, flowerTimelineId, photoRecords, time, photos
    );

    return FlowerTimelineItem(
        flowerTimelineId: flowerTimelineId,
        photos: savedRecords,
        datePrompt: calcDatePrompt(time, DateTime.now()),
        dateTime: time);
  }
}
